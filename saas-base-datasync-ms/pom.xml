<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>saas-base-datasync</artifactId>
        <groupId>com.xyy.saas.datasync</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>saas-base-datasync-ms</artifactId>
    <version>1.0-SNAPSHOT</version>

    <packaging>jar</packaging>

    <dependencies>
        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!--spring-web-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--spring-boot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <!--dubbo-->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <!--druid-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!--zk-->
        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <!--fast json-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <!--log-->
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
        </dependency>
        <!--apollo配置管理中心-->
        <dependency>
            <groupId>com.xyy.apollo</groupId>
            <artifactId>xyy-apollo-client</artifactId>
            <version>1.3.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.4</version>
        </dependency>
        <!--分布式id生产API-->
        <dependency>
            <groupId>com.xyy.leaf</groupId>
            <artifactId>leaf-api</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <!-- 大多情况下该包会引起冲突，酌情排除-->
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xyy.common</groupId>
            <artifactId>xyy-common-dubbo-client</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.xyy.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
            <version>1.2.0</version>
        </dependency>
        <!--datasync api-->
        <dependency>
            <groupId>com.xyy.saas.datasync</groupId>
            <artifactId>saas-base-datasync-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <!--CAT-->
        <!--<dependency>-->
        <!--    <groupId>com.xyy.cat</groupId>-->
        <!--    <artifactId>xyy-common-filter</artifactId>-->
        <!--    <version>3.1.0-SNAPSHOT</version>-->
        <!--</dependency>-->
        <!--  引入skywalking  -->
        <dependency>
            <groupId>com.xyy.apm</groupId>
            <artifactId>apm-micrometer-activation</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <artifactId>micrometer-core</artifactId>
            <groupId>io.micrometer</groupId>
            <version>1.5.4</version>
        </dependency>

        <dependency>
            <groupId>com.xyy.saas.rabbitmq.core</groupId>
            <artifactId>xyy-saas-rabbitmq-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas.clinic.center</groupId>
            <artifactId>clinic-center-mqtt-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--平滑发布-->
        <dependency>
            <groupId>com.xyy.common</groupId>
            <artifactId>xyy-common-dubbo-client</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xyy.user</groupId>
            <artifactId>xyy-saas-user-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>saas-mqtt-api</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas.yzh</groupId>
            <artifactId>xyy-saas-yzh-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot</artifactId>
            <version>2.0.3</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://maven.int.ybm100.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://maven.int.ybm100.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>