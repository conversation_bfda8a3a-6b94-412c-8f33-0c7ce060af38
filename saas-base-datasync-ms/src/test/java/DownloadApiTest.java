import com.alibaba.fastjson.JSON;
import com.xyy.saas.datasync.Application;
import com.xyy.saas.datasync.api.DownloadAPi;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.api.pojo.request.DownloadAllParam;
import com.xyy.saas.datasync.api.pojo.request.DownloadParam;
import com.xyy.saas.datasync.api.pojo.request.LoopCheckData;
import com.xyy.saas.datasync.api.pojo.request.LoopCheckParam;
import com.xyy.saas.datasync.api.pojo.response.DownloadAllResp;
import com.xyy.saas.datasync.api.pojo.response.DownloadResp;
import com.xyy.saas.datasync.api.pojo.response.LoopCheckResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles(value = "test")
@Slf4j
public class DownloadApiTest {
    @Autowired
    private DownloadAPi downloadAPi;

    @Test
    public void pullDataByVersionTest() throws ParseException{
        DownloadParam param = new DownloadParam();
        param.setBizLine("medicare");
        param.setOrganSign("ZHL0009529");
        param.setTableName("medicare_product");
//        param.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2020-12-21 11:00:00"));
        param.setCurrentVersion(612992L);
        param.setPageSize(100);

        Result<DownloadResp> result = downloadAPi.pullDataByVersion(param);

        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void pullDataAllTest(){
        DownloadAllParam param = new DownloadAllParam();
        param.setBizLine("saas");
        param.setOrganSign("ZHL00002534");
        param.setTableName("saas_incompatibility");
        param.setTableId(0L);
        param.setPageSize(100);

        Result<DownloadAllResp> result = downloadAPi.pullDataAll(param);

        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void loopCheckTest(){
        LoopCheckParam param = new LoopCheckParam();
        param.setBizLine("saas");
        param.setOrganSign("ZHL00002364");
        LoopCheckData checkData = new LoopCheckData();
        checkData.setCurrentVersion(1L);
        checkData.setTableName("saas_product_price_baseinfo");
        List<LoopCheckData> checkDataList = new ArrayList<>();
        checkDataList.add(checkData);
        param.setData(checkDataList);
        Result<LoopCheckResp> result = downloadAPi.loopCheck(param);
        System.out.println(JSON.toJSONString(result));
    }
}
