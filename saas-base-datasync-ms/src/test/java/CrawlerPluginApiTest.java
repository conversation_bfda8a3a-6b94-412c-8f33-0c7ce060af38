import com.alibaba.fastjson.JSON;
import com.xyy.saas.datasync.Application;
import com.xyy.saas.datasync.api.client.CrawlerPluginApi;
import com.xyy.saas.datasync.api.client.pojo.ClientInstance;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.service.EventService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2021/4/15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles(value = "test")
@Slf4j
public class CrawlerPluginApiTest {
    @Autowired
    private CrawlerPluginApi crawlerPluginApi;
    @Autowired
    private EventService eventService;

    @Test
    public void getHytClientInstanceTest(){
        Result<ClientInstance> hytClientInstance = crawlerPluginApi.getHytClientInstance("15871005853");
        System.out.println(JSON.toJSONString(hytClientInstance));
    }

    @Test
    public void deleteOldEventTest(){
        long begin = System.currentTimeMillis();
        eventService.deleteOldEvent(6, 10000);
        System.out.println("删除Event耗时:" + (System.currentTimeMillis() - begin));
    }
}
