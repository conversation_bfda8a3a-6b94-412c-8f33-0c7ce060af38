package com.xyy.saas.datasync.mq;

import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.config.MQInfo;
import com.xyy.saas.datasync.config.UploadConfigManager;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import javax.annotation.*;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class UploadProvider {
    private static final Logger logger = LoggerFactory.getLogger(UploadProvider.class);

    @Resource
    private UploadConfigManager uploadConfigManager;
    private Map<String, DefaultMQProducer> producers = new ConcurrentHashMap<>();

    @Value("${rocketMQ.mqServers}")
    private String mqServers;


    @PostConstruct
    public void start() {
        Map<String, MQInfo> mqInfos = uploadConfigManager.getMqs();

        if(!CollectionUtils.isEmpty(mqInfos)) {
            mqInfos.forEach((key, mqInfo) -> {
                start0(key, mqInfo.getGroup(), mqInfo.getTopic());
            });
        }
    }

    @PreDestroy
    public void stop() {
        producers.forEach((key, value) -> {
            value.shutdown();
        });
    }

    /**
     * 消息发送
     * @param key
     * @param topic
     * @param msg
     */
    public void send(String key, String topic, Object msg) {
        DefaultMQProducer producer = producers.get(key);

        if(producer == null) {
            throw new UnsupportedOperationException("未初始化的MQ，key=" + key + "，topic=" + topic + "，msg=" + msg);
        }

        Message message = new Message(topic, JSONObject.toJSONBytes(msg));

        try {
            // 同步发送
            SendResult sendResult = producer.send(message);

            if(sendResult == null || SendStatus.SEND_OK != sendResult.getSendStatus()) {
                logger.error("消息发送失败，sendResult={}", JSONObject.toJSONString(sendResult));
                throw new RuntimeException("消息发送失败，group=" + key + ", topic=" + topic + ", msg=" + msg);
            }
        }
        catch(Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }
    }

    private void start0(String key, String group, String topic) {
        DefaultMQProducer defaultMQProducer = new DefaultMQProducer(group);
        defaultMQProducer.setNamesrvAddr(mqServers);
        // https://wiki.int.ybm100.com/pages/viewpage.action?pageId=360119258
        defaultMQProducer.setSendMsgTimeout(10000);
        defaultMQProducer.setRetryTimesWhenSendFailed(3);

        try {
            defaultMQProducer.start();
            producers.put(key, defaultMQProducer);
            logger.info("upload provider is running, key={}, group={}, topic={}", key, group, topic);
        }
        catch(Exception ex) {
            logger.error(ex.getMessage() + ", group=" + group + ", topic=" + topic + ", key=" + key, ex);
        }
    }
}
