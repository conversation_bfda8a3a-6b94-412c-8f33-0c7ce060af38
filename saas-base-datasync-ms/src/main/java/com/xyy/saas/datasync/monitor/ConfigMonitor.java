package com.xyy.saas.datasync.monitor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.NamedThreadFactory;
import com.xyy.saas.datasync.config.DownloadConfigManager;
import com.xyy.saas.datasync.config.UploadConfigManager;
import com.xyy.saas.datasync.enums.ConfigTypeEnum;
import com.xyy.saas.datasync.pojo.ConfigDo;
import com.xyy.saas.datasync.service.ConfigService;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class ConfigMonitor {
    private static final Logger logger = LoggerFactory.getLogger(ConfigMonitor.class);
    private ScheduledExecutorService executor = Executors.newScheduledThreadPool(2,
            new NamedThreadFactory("remote-load-config-scan"));
    private long downloadConfigTime = System.currentTimeMillis();
    private long uploadConfigTime = System.currentTimeMillis();

    @Resource
    private ConfigService configService;
    @Resource
    private DownloadConfigManager downloadConfigManager;
    @Resource
    private UploadConfigManager uploadConfigManager;

    @PostConstruct
    public void startMonitor() {
        // 监听DownloadConfig变化
        executor.scheduleWithFixedDelay(() -> {
            try {
                loadDownloadConfig();
            } catch (Throwable e) {
                logger.error("scan remote DownloadConfig failed", e);
            }
        }, 30, 30, TimeUnit.SECONDS);

        // 监听UploadConfig变化
        executor.scheduleWithFixedDelay(() -> {
            try {
                loadUploadConfig();
            } catch (Throwable e) {
                logger.error("scan remote UploadConfig failed", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }

    private void loadDownloadConfig() {
        List<ConfigDo> list = configService.listConfig(ConfigTypeEnum.DOWNLOAD.getType(), new Date(downloadConfigTime));

        if(CollectionUtils.isEmpty(list)) {
            return;
        }

        downloadConfigTime = list.get(NumberUtils.INTEGER_ZERO).getUpdateTime().getTime();
        downloadConfigManager.load(list);
    }

    private void loadUploadConfig() {
        List<ConfigDo> list = configService.listConfig(ConfigTypeEnum.UPLOAD.getType(), new Date(uploadConfigTime));

        if(CollectionUtils.isEmpty(list)) {
            return;
        }

        uploadConfigTime = list.get(NumberUtils.INTEGER_ZERO).getUpdateTime().getTime();
        uploadConfigManager.load(list);
    }
}
