package com.xyy.saas.datasync.mq;

import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.config.DownloadConfigManager;
import com.xyy.saas.datasync.config.MQInfo;
import com.xyy.saas.datasync.service.CanalConsumerService;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.*;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import javax.annotation.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class CanalConsumer {
    private static final Logger logger = LoggerFactory.getLogger(CanalConsumer.class);

    @Resource
    private CanalConsumerService canalConsumerService;
    @Resource
    private DownloadConfigManager downloadConfigManager;

    private List<DefaultMQPushConsumer> consumers = new ArrayList<>();

    @Value("${rocketMQ.mqServers}")
    private String mqServers;
    @Value("${rocketMQ.batchSize}")
    private String batchSize;

    @PostConstruct
    public void start() {
        Set<MQInfo> mqs = downloadConfigManager.getMqs();

        mqs.forEach(mq -> {
            start0(mq.getGroup(), mq.getTopic());
        });
    }

    @PreDestroy
    public void stop() {
        if(!CollectionUtils.isEmpty(consumers)) {
            consumers.forEach(DefaultMQPushConsumer::shutdown);
            logger.info("Canal consume shutdown ！！！！！！！");
        }
    }

    /**
     *
     * @param group
     * @param topic
     */
    private void start0(String group, String topic) {
        try {
            if(StringUtils.isEmpty(group)) {
                throw new RuntimeException("Canal group is null");
            }
            if(StringUtils.isEmpty(mqServers)) {
                throw new RuntimeException("Canal servers is null");
            }
            if(StringUtils.isEmpty(topic)) {
                throw new RuntimeException("Canal topic is null");
            }

            DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(group);
            consumer.setNamesrvAddr(mqServers);

            if(!StringUtils.isEmpty(batchSize)) {
                consumer.setConsumeMessageBatchMaxSize(Integer.parseInt(batchSize));
            }

            consumer.subscribe(topic ,"*");
            consumer.registerMessageListener(new MessageListenerOrderly() {

                @Override
                public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
                    try {
                        long start = System.currentTimeMillis();
                        handle(list);
                        long end = System.currentTimeMillis();

                        if(end - start > 100) {
                            logger.warn("Consume canal message cost:{}, size:{}", (end - start), list.size());
                        }
                    }
                    catch(Exception ex) {
                        logger.error(ex.getMessage() + ", data=" + JSONObject.toJSONString(list), ex);
                        /**
                         * TODO
                         */
                        return ConsumeOrderlyStatus.SUCCESS;
                        // return ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
                    }

                    return ConsumeOrderlyStatus.SUCCESS;
                }
            });

            consumer.start();
            consumers.add(consumer);
            logger.info("Canal consume running ！！！！！！！ ,group:{}, topic:{} ", group, topic);
        }
        catch(Exception ex) {
            logger.error(ex.getMessage(), ex);
        }
    }

    /**
     * 处理Canal消息
     * @param list
     */
    private void handle(List<MessageExt> list) {
        canalConsumerService.handleBatch(list);
    }
}
