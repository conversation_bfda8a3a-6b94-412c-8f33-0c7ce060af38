package com.xyy.saas.datasync.service;

import com.xyy.saas.datasync.mapper.ClearRecordMapper;
import com.xyy.saas.datasync.rpc.clrtc.ClearDto;
import com.xyy.saas.datasync.rpc.clrtc.ClearRecordDo;
import com.xyy.saas.datasync.rpc.clrtc.ClearStatDo;
import com.xyy.saas.medicare.core.util.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClearRecordService {
    @Resource
    private ClearRecordMapper clearRecordMapper;
    @Resource
    private EventService eventService;

    /**
     * 分组统计
     * @return
     */
    public List<ClearStatDo> stat(int overLimit) {
        List<ClearStatDo> stat = clearRecordMapper.stat(overLimit);
        log.info("[数据 统计] 统计结果: {}", stat == null ? 0 : stat.size());
        if (CollectionUtils.isEmpty(stat)) {
            return new ArrayList<>(0);
        }
        return stat;
    }

    /**
     * 分组统计 并写入数据库
     * @return
     */
    public void statAndRecord(ClearStatDo s) {
        ClearRecordDo ist = new ClearRecordDo();
        ist.setBizLine(s.getBizLine());
        ist.setOrganSign(s.getOrganSign());
        ist.setTargetTable(s.getTargetTable());
        ist.setCnt(s.getCnt());
        ist.setMaxId(s.getMaxId());
        clearRecordMapper.insertOrUpdate(ist);
    }

    /**
     * 查询需要删除的组合
     * @return
     */
    public List<ClearRecordDo> listForClear() {
        List<ClearRecordDo> list = clearRecordMapper.listForClear();
        log.info("[数据 清除] 待清理数量: {}", list == null ? 0 : list.size());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list;
    }

    /**
     * 清除event数据 并写入数据库
     * @return
     */
    public int clearAndRecord(ClearRecordDo d) {
        ClearDto clearDto = new ClearDto(d.getBizLine(), d.getOrganSign(), d.getTargetTable());
        clearDto.setCurrentVersion(d.getCurrentVersion());
        String jst = JSONUtil.getJsonString(clearDto);

        log.info("[数据 清除] 开始: {}", jst);
        int count = eventService.clearOld(clearDto, false);

        d.setLastDelete((long) count);
        d.setLastVersion(d.getCurrentVersion());
        clearRecordMapper.insertOrUpdate(d);
        log.info("[数据 清除] 结束: {}, 清除条数: {}", jst, count);
        return count;
    }

    /**
     * 缓存版本信息 写入数据库
     * @return
     */
    public void flushCacheToDb(ClearDto d) {
        ClearRecordDo ist = new ClearRecordDo();
        ist.setBizLine(d.getBizLine());
        ist.setOrganSign(d.getOrganSign());
        ist.setTargetTable(d.getTargetTable());
        ist.setCurrentVersion(d.getCurrentVersion());
        ist.setNeedClear(1);
        clearRecordMapper.insertOrUpdate(ist);
    }
}
