package com.xyy.saas.datasync.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.datasync.api.client.ClientConstant;
import com.xyy.saas.datasync.api.client.CrawlerPluginApi;
import com.xyy.saas.datasync.api.client.pojo.ClientConfig;
import com.xyy.saas.datasync.api.client.pojo.ClientInstance;
import com.xyy.saas.datasync.api.client.pojo.UploadParam;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.config.UploadConfigManager;
import com.xyy.saas.datasync.mapper.ClientDataMapper;
import com.xyy.saas.datasync.mq.UploadProvider;
import com.xyy.saas.datasync.service.ClientConfigService;
import com.xyy.saas.datasync.service.ClientInstanceService;
import com.xyy.saas.datasync.service.EventService;
import com.xyy.saas.yzh.dataaccess.dto.DbConnInfoDto;
import com.xyy.saas.yzh.datasync.HytDataSyncApi;
import com.xyy.saas.yzh.datasync.dto.ErpInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service(version = "0.0.1")
@SuppressWarnings("unchecked")
public class CrawlerPluginApiImpl implements CrawlerPluginApi {
    private static final Logger logger = LoggerFactory.getLogger(CrawlerPluginApiImpl.class);

    @Resource
    private ClientConfigService clientConfigService;
    @Resource
    private ClientInstanceService clientInstanceService;
    @Resource
    private UploadProvider uploadProvider;
    @Resource
    private UploadConfigManager uploadConfigManager;
    @Resource
    private ClientDataMapper clientDataMapper;
    @Reference(version = "0.0.1")
    private HytDataSyncApi hytDataSyncApi;
    @Resource
    private EventService eventService;

    /**
     * 根据外部ID查询配置信息
     *
     * @param bizLine     业务线
     * @param outUniqueId 唯一ID
     * @return
     */
    @Override
    public Result<ClientConfig> getConfig(String bizLine, Long outUniqueId) {
        return Result.success(clientConfigService.getConfig(bizLine, outUniqueId));
    }

    /**
     * 根据外部ID查询实例信息
     *
     * @param bizLine     业务线
     * @param outUniqueId 企业ID
     * @return
     */
    @Override
    public Result<ClientInstance> getInstance(String bizLine, Long outUniqueId) {
        try {
            return Result.success(clientInstanceService.getInstance(bizLine, outUniqueId));
        }
        catch(Exception ex) {
            logger.error(ex.getMessage(), ex);
            return Result.fail(ex.getMessage());
        }
    }

    /**
     * 根据客户端id，客户端key 获取实例
     *
     * @param clientId
     * @param clientKey
     * @return
     */
    @Override
    public Result<ClientInstance> getClientInstance(Long clientId, String clientKey) {
        try {
            return Result.success(clientInstanceService.getClientInstance(clientId, clientKey));
        }
        catch(Exception ex) {
            logger.error(ex.getMessage(), ex);
            return Result.fail(ex.getMessage());
        }
    }

    /**
     * 抓取数据上传
     *
     * @param param
     * @return
     */
    @Override
    public Result upload(UploadParam param) {
        try {
            // 1.参数校验
            UploadParam.check(param);

            /*
            抓取的数据不再存储在本系统的数据库表中. 2020-12-24
            try {
                // 存储原始数据
                clientDataMapper.insert(ClientData.build(param));
            }
            catch(Exception ex) {
                logger.error(ex.getMessage(), ex);
            }*/

            // 2.转发数据
            String key = ClientConstant.CRAWLER_PLUGIN_PREFIX + param.getBizLine();
            String topic = uploadConfigManager.getTopic(key);
            uploadProvider.send(key, topic, param);

            /**
             * 全量抓取不上报偏移量
             *  在混合模式下，全量抓取上报偏移量在某种场景下回出问题！！！！
             */
            if(param.getCurrentOffset() != null || param.getFirstRun() != null) {
                // 3.更新任务偏移量
                Map<String, Object> updateParam = new HashMap<>();

                if(param.getCurrentOffset() != null) {
                    updateParam.put("currentOffset", param.getCurrentOffset());
                }

                if(param.getFirstRun() != null) {
                    updateParam.put("firstRun", param.getFirstRun());
                }

                clientInstanceService.updateTaskInfo(param.getBizLine(), param.getOutUniqueId(), param.getTaskId(), updateParam);
            }

            return Result.success();
        }
        catch(Exception ex) {
            logger.error(ex.getMessage(), ex);
            return Result.fail(ex.getMessage());
        }
    }

    /**
     * 根据荷叶通账号获取实例
     *
     * @param hytAcount
     * @return
     */
    @Override
    public Result<ClientInstance> getHytClientInstance(String hytAcount){
        ResultVO<ErpInfoDto> erpInfoResult = hytDataSyncApi.getErpInfoByAccount(hytAcount);
        ErpInfoDto erpInfo = erpInfoResult.getResult();
        if (erpInfo == null) {
            return Result.fail(erpInfoResult.getMsg());
        }
        long clientId = clientInstanceService.convertClientIdByErpInfo(erpInfo);
        String clientKey = clientInstanceService.convertClientKeyByErpInfo(erpInfo);
        ClientInstance clientInstance = clientInstanceService.getClientInstance(clientId, clientKey);
        if (clientInstance == null) {
            Result<Boolean> insertResult = clientInstanceService.insertHytClientInstance(erpInfo);
            if (!insertResult.isSuccess()) {
                return Result.fail(insertResult.getMsg());
            }
            clientInstance = clientInstanceService.getClientInstance(clientId, clientKey);
        }
        String instanceConfig = this.wrapHytDataSource(erpInfo, clientInstance.getInstanceConfig());
        clientInstance.setInstanceConfig(instanceConfig);

        return Result.success(clientInstance);
    }

    /**
     * 删除N个月前的Event数据
     *
     * @param month
     * @param pageSize
     * @return
     */
    @Override
    public Result<Boolean> deleteOldEvent(int month, int pageSize){
        try {
            eventService.deleteOldEvent(month, pageSize);
        } catch (Exception e) {
            logger.error("删除Event数据异常", e);
            return Result.fail("删除Event数据异常");
        }
        return Result.success(Boolean.TRUE);
    }

    private String wrapHytDataSource(ErpInfoDto erpInfo, String instanceConfig){
        Map<String, String> dataSourceMap = new HashMap<>();
        DbConnInfoDto dbConnInfo = erpInfo.getDbConnInfoDto();
        String dbType = erpInfo.getDbType();
        dataSourceMap.put("dbType", dbType);
        dataSourceMap.put("userName", dbConnInfo.getUsername());
        dataSourceMap.put("password", dbConnInfo.getPassword());
        String url = null;
        switch (dbType) {
            case "SqlServer":
            {
                url = "jdbc:sqlserver://" + dbConnInfo.getDatabaseAddress() + ":" + dbConnInfo.getPort() + ";DatabaseName=" + dbConnInfo.getDatabaseName();
                if (StringUtils.isBlank(dbConnInfo.getUsername()) || StringUtils.isBlank(dbConnInfo.getPassword())) {
                    url += ";integratedSecurity=true";
                }
                break;
            }
            case "MySQL":
            {
                url = "jdbc:mysql://" + dbConnInfo.getDatabaseAddress() + ":" + dbConnInfo.getPort() + "/" + dbConnInfo.getDatabaseName();
                break;
            }
            case "Oracle":
            {
                url = "jdbc:oracle:thin:@//" + dbConnInfo.getDatabaseAddress() + ":" + dbConnInfo.getPort() + "/" + dbConnInfo.getDatabaseName();
                break;
            }
            default: break;
        }
        dataSourceMap.put("url", url);

        JSONObject jsonObject = JSON.parseObject(instanceConfig);
        jsonObject.put("source", dataSourceMap);
        return JSON.toJSONString(jsonObject);
    }
}
