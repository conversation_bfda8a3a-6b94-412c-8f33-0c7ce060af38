package com.xyy.saas.datasync.mapper;

import com.xyy.saas.datasync.api.client.pojo.ClientConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 客户端配置
 */
@Mapper
public interface ClientConfigMapper {
    /**
     * 根据条件查询
     * @param clientConfig
     * @return
     */
    List<ClientConfig> list(ClientConfig clientConfig);

    /**
     * 新增/编辑
     * @param clientConfig
     */
    void upsert(ClientConfig clientConfig);
}