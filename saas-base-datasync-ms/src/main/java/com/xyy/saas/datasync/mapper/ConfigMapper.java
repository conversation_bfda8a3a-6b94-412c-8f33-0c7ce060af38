package com.xyy.saas.datasync.mapper;

import com.xyy.saas.datasync.pojo.ConfigDo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 配置Mapper
 */
@Mapper
public interface ConfigMapper {
    /**
     * 插入
     * @param configDo
     * @return
     */
    int insert(ConfigDo configDo);

    /**
     * 查询列表
     * @param configDo
     * @return
     * ConfigDo getConfig(String bizLine, Integer configType, String sourceTable);
     */
    List<ConfigDo> list(ConfigDo configDo);
}
