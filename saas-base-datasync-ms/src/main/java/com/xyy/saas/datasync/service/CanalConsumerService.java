package com.xyy.saas.datasync.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.common.Constant;
import com.xyy.saas.datasync.config.DownloadConfigManager;
import com.xyy.saas.datasync.enums.ConfigTypeEnum;
import com.xyy.saas.datasync.enums.DataSyncTypeEnum;
import com.xyy.saas.datasync.enums.EventTypeEnum;
import com.xyy.saas.datasync.mq.MQTTProvider;
import com.xyy.saas.datasync.pojo.ConfigDo;
import com.xyy.saas.datasync.pojo.EventDo;
import com.xyy.saas.datasync.api.pojo.request.FlatMessage;
import com.xyy.saas.datasync.pojo.param.BatchEventParam;
import com.xyy.saas.datasync.remote.DataSyncIgnoreAdapterRemote;
import com.xyy.saas.datasync.utils.BizUtil;
import com.xyy.saas.datasync.utils.CommonUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.enums.BizModelEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageConst;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * canal消息消费处理逻辑
 */
@Service
public class CanalConsumerService {
    private static final Logger logger = LoggerFactory.getLogger(CanalConsumerService.class);

    @Resource
    private ConfigService configService;
    @Resource
    private EventService eventService;
    @Resource
    private DownloadConfigManager downloadConfigManager;
    @Resource
    private MQTTProvider mqttProvider;
    @Resource
    private DataSyncIgnoreAdapterRemote dataSyncIgnoreAdapterRemote;
    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;
    /**
     * 去baseVersion灰度级别(null或0:关闭灰度功能; 1:开启灰度; 2:全量)
     */
    @Value("${datasync.grayscale.baseversion.level:0}")
    private Integer grayscaleLevel;
    /**
     * 灰度机构(机构号,用井号包裹连接. 例: #a#b#c#, 如果是全部机构,则包含"ALL"或者直接ALL)
     */
    @Value("${datasync.grayscale.baseversion.organSigns}")
    private String grayscaleOrganSigns;
    /**
     * 灰度表(表名,用井号包裹连接. 例: #a#b#c#, 如果是全部表名,则包含"ALL"或者直接ALL)
     */
    @Value("${datasync.grayscale.baseversion.tables}")
    private String grayscaleTables;
    /**
     * 去BaseVersion之前接入的表
     */
    @Value("${datasync.old.tables}")
    private String oldTables;

    /**
     * 不触发通知的表(表名,用逗号连接. 例: a,b,c)
     */
    @Value("#{'${datasync.notify.exclude.tables:}'.split(',')}")
    private Set<String> notifyExcludeTables;

    public void handleBatch(List<MessageExt> list) {
        if(CollectionUtils.isEmpty(list)) {
            return;
        }

        List<BatchEventParam> bepList = list.stream().map(messageExt -> {
            byte[] body = messageExt.getBody();
            if(CommonUtil.isEmpty(body)) {
                return null;
            }
            FlatMessage fm = JSONObject.parseObject(body, FlatMessage.class);
            if (fm == null || CollectionUtils.isEmpty(fm.getData())) {
                return null;
            }
            String bizLine = downloadConfigManager.getBizLine(messageExt.getTopic());
            // 0.医保商品删除
            if (Constant.BIZLINE_MEDICARE.equalsIgnoreCase(bizLine)
                    && "medicare_product".equalsIgnoreCase(fm.getTable())
                    && EventTypeEnum.DELETE.getType().equalsIgnoreCase(fm.getType())) {
                // 由医保云决定是否忽略 处理
                if (dataSyncIgnoreAdapterRemote.ignoreMatch(fm)) {
                    return null;
                }
            }
            // 处理消息
            return handle(bizLine, fm, getUniqueKey(messageExt));
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(bepList)) {
            return;
        }

        List<EventDo> flatList = bepList.stream().flatMap(i -> Optional.ofNullable(i.getEventDoList()).map(List::stream).orElseGet(Stream::empty)).collect(Collectors.toList());
        // 批量写数据库
        eventService.batchInsert(flatList);

        // 通知客户端同步数据
        bepList.stream().collect(Collectors.toMap(
                BatchEventParam::getConfigDo,
                BatchEventParam::getNoticeOrganSign,
                (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                }
                )).forEach(this::notifyDataSyncMqtt);
    }

    /**
     * GET PROPERTY_UNIQ_CLIENT_MESSAGE_ID_KEYIDX = "UNIQ_KEY"
     * @param messageExt
     * @return
     */
    private String getUniqueKey(MessageExt messageExt) {
        try {
            return messageExt.getProperty(MessageConst.PROPERTY_UNIQ_CLIENT_MESSAGE_ID_KEYIDX);
        }
        catch(Exception ex) {
            logger.error(ex.getMessage(), ex);
        }

        return messageExt.getMsgId();
    }

    /**
     * 处理Canal消息
     * @param message
     */
    public BatchEventParam handle(String bizLine, FlatMessage message, String messageId) {
        if(message == null) {
            return null;
        }

        // 1.查询同步类型
        ConfigDo configDo = configService.getConfig(bizLine, ConfigTypeEnum.DOWNLOAD.getType(), message.getTable());

        if(configDo == null) {
            throw new RuntimeException("未查询到同步配置，messageId=" + messageId);
        }

        // 2.查询通知列表
        DataSyncTypeEnum dataSyncType = DataSyncTypeEnum.get(configDo.getDatasyncType());

        if(dataSyncType == null) {
            throw new RuntimeException("不支持的同步类型，dataSyncType=" + configDo.getDatasyncType());
        }

        List<Map<String, String>> items =  message.getData();
        // 通知列表
        Set<String> noticeOrganSign = new HashSet<>();
        // 批量插入event
        List<EventDo> eventDoList = new ArrayList<>();

        // 3.事件存储
        String sourceTable = configDo.getSourceTable();
        String targetTable = configDo.getTargetTable();

        items.forEach(item -> {
            String organSign = BizUtil.getOrganSignInObject(item);
            if (StringUtils.isBlank(organSign)) {
                return;
            }

            // 灰度特殊处理
                // 判断是否是灰度表
            if (grayscaleLevel == 1 && this.checkContains(grayscaleTables, targetTable)){
                // 判断是否是灰度表,灰度机构. 不是,则不写入event数据
                if (!this.isNewTable(organSign, targetTable)) {
                    return;
                }
            }

            Long sourceTableId = BizUtil.getId(item);
            String eventType = message.getType();
            Date bizDataUpdateTime = BizUtil.getBizDataUpdateTime(eventType, item);

            switch(dataSyncType) {
                // 一对一
                case SINGLE:
                    eventDoList.add(EventDo.build(bizLine, organSign, organSign, sourceTable,
                                                  targetTable, sourceTableId, eventType, bizDataUpdateTime));
                    noticeOrganSign.add(organSign);
                    break;
                // 一对多
                case MANY:
                    // 根据门店机构号获取总部机构号
                    List<String> subOrganSigns = this.getSubOrganSigns(organSign);
                    subOrganSigns.forEach(subOrganSign -> {
                        eventDoList.add(EventDo.build(bizLine, organSign, subOrganSign, sourceTable,
                                                      targetTable, sourceTableId, eventType, bizDataUpdateTime));
                    });
                    noticeOrganSign.addAll(subOrganSigns);
                    break;
                // 全量
                case ALL:
                    eventDoList.add(EventDo.build(bizLine, null, null, sourceTable,
                                                  targetTable, sourceTableId, eventType, bizDataUpdateTime));
                    break;
                default:
                    throw new UnsupportedOperationException(dataSyncType.getDesc());
            }
        });

        return BatchEventParam.build(configDo, eventDoList, noticeOrganSign);
    }

    private void notifyDataSyncMqtt(ConfigDo configDo, Set<String> noticeOrganSign) {
        String bizLine = configDo.getBizLine();
        String targetTable = configDo.getTargetTable();
        try {
            List<String> tableNameList = new ArrayList<>();
            if (StringUtils.isNotBlank(configDo.getTableGroup())) {
                List<ConfigDo> configDoList = configService.queryByGroup(bizLine, ConfigTypeEnum.DOWNLOAD.getType(), configDo.getTableGroup());
                if (!CollectionUtils.isEmpty(configDoList)) {
                    configDoList.forEach(config -> {
                        tableNameList.add(config.getTargetTable());
                    });
                }
            }
            if (CollectionUtils.isEmpty(tableNameList)) {
                tableNameList.add(targetTable);
            }
            // 过滤不触发通知的表
            List<String> notifyTableNameList = tableNameList.stream().filter(i -> !notifyExcludeTables.contains(i)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(notifyTableNameList)) {
                return;
            }
            if(DataSyncTypeEnum.ALL.getType().equals(configDo.getDatasyncType())) {
                mqttProvider.sendMulticast(bizLine, targetTable, tableNameList);
            } else {
                mqttProvider.sendByOrganSign(bizLine, this.getNoticeOrgans(noticeOrganSign, targetTable), targetTable, tableNameList);
            }
        }
        catch(Exception ex) {
            logger.error(ex.getMessage() + ", MQTT通知失败，noticeOrganSign=" + JSONObject.toJSONString(noticeOrganSign), ex);
        }
    }

    /**
     * 根据总部机构号获取所有门店机构号(表同步方式为一对多时使用)
     * @param organSign
     * @return
     */
    private List<String> getSubOrganSigns(String organSign){
        List<String> subOrganSigns = new ArrayList<>();
        if (StringUtils.isBlank(organSign)) {
            return subOrganSigns;
        }

        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        if (drugstore == null) {
            return subOrganSigns;
        }

        if (drugstore.getBizModel() == BizModelEnum.JOINT_OPERATION.getKey() || drugstore.getBizModel() == BizModelEnum.ONLY.getKey()) {
            // 单体与联营机构特殊处理,不需要查询门店机构号
            subOrganSigns.add(organSign);
        } else {
            List<SaaSDrugstoreDto> subOrgan = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            if (!CollectionUtils.isEmpty(subOrgan)) {
                subOrganSigns = subOrgan.stream().map(SaaSDrugstoreDto::getOrganSign).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(subOrganSigns)) {
                subOrganSigns.add(organSign);
            }
        }
        return subOrganSigns;
    }

    /**
     * 根据白名单过滤通知的机构
     * @param organSigns
     * @param tableName
     * @return
     */
    private Set<String> getNoticeOrgans(Set<String> organSigns, String tableName){
        // 旧逻辑接入的表,不走灰度过滤
        if (checkContains(oldTables, tableName)) {
            return organSigns;
        }

        return organSigns.stream().filter(s -> isNewTable(s, tableName)).collect(Collectors.toSet());
    }

    /**
     * 分隔符
     */
    private static final String SPLIT_SYMBOL = "#";
    /**
     * 标识---全部
     */
    private static final String ALL_ORGAN_TABLE = "ALL";

    /**
     * 检查是否新表(去baseVersion)
     */
    public boolean isNewTable(String organSign, String tableName){
        // 关闭新功能灰度,都走旧的同步
        if (grayscaleLevel == null || grayscaleLevel == 0) {
            return Boolean.FALSE;
        }
        // 开启灰度
        if (grayscaleLevel == 1) {
            // 灰度机构和表都为空
            if (StringUtils.isBlank(grayscaleTables) || StringUtils.isBlank(grayscaleOrganSigns)) {
                return Boolean.FALSE;
            }
            // 灰度机构包含#ALL#,则代表灰度所有机构号
            if (checkContains(grayscaleOrganSigns, ALL_ORGAN_TABLE)) {
                // 灰度表包含目标表
                if (checkContains(grayscaleTables, tableName)) {
                    return Boolean.TRUE;
                }
            }
            // 灰度表包含#xxxtableALL#,则代表目标表对所有机构号开启灰度
            if (checkContains(grayscaleTables, tableName.trim() + ALL_ORGAN_TABLE)) {
                return Boolean.TRUE;
            }
            // 灰度机构包含目标机构 && 灰度表包含目标表
            // 灰度表未包含目标表
            if (checkContains(grayscaleOrganSigns, organSign) && checkContains(grayscaleTables, tableName)) {
                return Boolean.TRUE;

            }
        }
        // 全量
        if (grayscaleLevel == 2) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 检查源字符串是否包含#目标#字符串
     * @param source
     * @param target
     * @return
     */
    private boolean checkContains(String source, String target){
        if (StringUtils.isBlank(source) || StringUtils.isBlank(target)) {
            return false;
        }
        return source.contains(SPLIT_SYMBOL + target.trim() + SPLIT_SYMBOL);
    }
}
