package com.xyy.saas.datasync.config;

import java.util.Objects;

/**
 * MQ信息
 */
public class MQInfo {
    private String group;
    private String topic;
    private String bizLine;

    /**
     * 构建对象
     * @param group
     * @param topic
     * @return
     */
    public static MQInfo build(String group, String topic, String bizLine) {
        MQInfo info = new MQInfo();
        info.setGroup(group);
        info.setTopic(topic);
        info.setBizLine(bizLine);
        return info;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MQInfo mqInfo = (MQInfo) o;
        return Objects.equals(group, mqInfo.group) &&
                Objects.equals(topic, mqInfo.topic);
    }

    @Override
    public int hashCode() {
        return Objects.hash(group, topic);
    }
}
