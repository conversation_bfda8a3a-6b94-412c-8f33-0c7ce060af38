package com.xyy.saas.datasync.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.datasync.api.pojo.request.BaseDownloadAllParam;
import com.xyy.saas.datasync.api.pojo.request.DownloadAllParam;
import com.xyy.saas.datasync.api.pojo.request.DownloadParam;
import com.xyy.saas.datasync.api.pojo.response.BaseDownloadAllResp;
import com.xyy.saas.datasync.config.DownloadConfigManager;
import com.xyy.saas.datasync.enums.ConfigTypeEnum;
import com.xyy.saas.datasync.enums.DataSyncTypeEnum;
import com.xyy.saas.datasync.enums.ExceptionCodeEnum;
import com.xyy.saas.datasync.pojo.ConfigDo;
import com.xyy.saas.datasync.service.ConfigService;
import com.xyy.saas.datasync.spi.DataSyncAdapter;
import com.xyy.saas.datasync.utils.BizUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.enums.BizModelEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class DataSyncAdapterRemote {
    private static final Logger logger = LoggerFactory.getLogger(DataSyncAdapterRemote.class);

    @Resource
    private DownloadConfigManager downloadConfigManager;
    @Resource
    private ConfigService configService;
    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    /**
     * 根据父机构获取子机构/根据机构获取通知机构
     * @param organSign
     * @return
     */
    @Deprecated
    public List<String> listSubOrganSign(String bizLine, String table, String organSign) {
        if(StringUtils.isEmpty(organSign)) {
            throw new RuntimeException(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        return getAdapter(bizLine, table).listSubOrganSign(organSign);
    }

    /**
     * 根据表名和ID，查询数据列表
     * @param table
     * @param ids
     * @return
     */
    public Map<Long, Object> listDataByIds(String bizLine, String organSign, String table, List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            throw new RuntimeException(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }
        Map<Long, Object> objectMap = getAdapter(bizLine, table).listDataByIds(table, ids);
        if (CollectionUtils.isEmpty(objectMap)) {
            return objectMap;
        }

        // 判断是否同步方式是否为一对多;如果是,则需要将返回结果中的机构号替换为门店机构号
        ConfigDo config = configService.getConfig(bizLine, ConfigTypeEnum.DOWNLOAD.getType(), table);
        Integer datasyncType = config.getDatasyncType();
        if (DataSyncTypeEnum.MANY.getType().equals(datasyncType)) {
            SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
            if (drugstore.getBizModel() != BizModelEnum.JOINT_OPERATION.getKey()) {
                // 联营机构不需要进行替换
                objectMap.forEach((k, o) -> BizUtil.setOrganSignInObject(o, organSign));
            }
        }

        return objectMap;
    }

    /**
     * 分页查询(根据主键ID偏移分页)
     * @param param
     * @return
     */
    public BaseDownloadAllResp listData(DownloadAllParam param) {
        if(param == null) {
            throw new RuntimeException(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        param.check();
        String sourceTable = configService.getSourceTable(param.getBizLine(), ConfigTypeEnum.DOWNLOAD.getType(), param.getTableName());
        BaseDownloadAllParam remoteParam = new BaseDownloadAllParam();
        BeanUtils.copyProperties(param, remoteParam);
        remoteParam.setTableName(sourceTable);

        ConfigDo config = configService.getConfig(param.getBizLine(), ConfigTypeEnum.DOWNLOAD.getType(), sourceTable);
        Integer datasyncType = config.getDatasyncType();

        BaseDownloadAllResp allResp = getAdapter(param.getBizLine(), remoteParam.getTableName()).listData(remoteParam);
        if (allResp == null || CollectionUtils.isEmpty(allResp.getData())) {
            return allResp;
        }

        // 判断是否同步方式是否为一对多;如果是,则需要将返回结果中的机构号替换为门店机构号
        if (DataSyncTypeEnum.MANY.getType().equals(datasyncType)) {
            List<Object> data = allResp.getData();
            SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(param.getOrganSign());
            // 联营机构不需要替换机构号
            if (drugstore.getBizModel() != BizModelEnum.JOINT_OPERATION.getKey()) {
                // 替换总部机构号为门店机构号
                data.forEach(o -> BizUtil.setOrganSignInObject(o, param.getOrganSign()));
            }
        }

        return allResp;
    }

    /**
     * 分页查询(根据updateTime)
     * @param param
     * @return
     */
    public BaseDownloadAllResp listDataByUpdateTime(DownloadParam param) {
        if(param == null) {
            throw new RuntimeException(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        param.check();
        String sourceTable = configService.getSourceTable(param.getBizLine(), ConfigTypeEnum.DOWNLOAD.getType(), param.getTableName());
        DataSyncAdapter adapter = getAdapter(param.getBizLine(), sourceTable);
        BaseDownloadAllParam remoteParam = new BaseDownloadAllParam();
        BeanUtils.copyProperties(param, remoteParam);
        remoteParam.setTableName(sourceTable);

        ConfigDo config = configService.getConfig(param.getBizLine(), ConfigTypeEnum.DOWNLOAD.getType(), sourceTable);
        Integer datasyncType = config.getDatasyncType();

        BaseDownloadAllResp allResp = adapter.listDataByUpdateTime(remoteParam);

        if (allResp == null || CollectionUtils.isEmpty(allResp.getData())) {
            return allResp;
        }

        List<Object> data = allResp.getData();
        Date maxUpdateTime = allResp.getMaxUpdateTime();
        remoteParam.setUpdateTime(maxUpdateTime);
        BaseDownloadAllResp equalUpdateTimeResp = adapter.listDataEqualUpdateTime(remoteParam);
        List<Object> equalUpdateTimeRespData = equalUpdateTimeResp.getData();
        if (!CollectionUtils.isEmpty(equalUpdateTimeRespData)) {
            data.addAll(equalUpdateTimeRespData);
        }

        // 判断是否同步方式是否为一对多;如果是,则需要将返回结果中的机构号替换为门店机构号
        if (DataSyncTypeEnum.MANY.getType().equals(datasyncType)) {
            SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(param.getOrganSign());
            // 联营机构不需要替换机构号
            if (drugstore.getBizModel() != BizModelEnum.JOINT_OPERATION.getKey()) {
                data.forEach(o -> BizUtil.setOrganSignInObject(o, param.getOrganSign()));
            }
        }

        return allResp;
    }

    /**
     * 获取DataSyncAdapter
     * @param bizLine
     * @param table
     * @return
     */
    private DataSyncAdapter getAdapter(String bizLine, String table) {
        if(StringUtils.isEmpty(bizLine) || StringUtils.isEmpty(table)) {
            logger.error("根据父机构获取子机构失败，bizLine={}, table={}", bizLine, table);
            throw new RuntimeException(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        DataSyncAdapter adapter = downloadConfigManager.getDataSyncAdapter(bizLine, table);

        if(adapter == null) {
            throw new RuntimeException("未匹配到DataSyncAdapter，bizLine=" + bizLine + ", table=" + table);
        }

        return adapter;
    }
}
