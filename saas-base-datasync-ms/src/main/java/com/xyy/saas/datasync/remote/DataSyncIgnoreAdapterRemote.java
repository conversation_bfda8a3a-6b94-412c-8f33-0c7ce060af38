package com.xyy.saas.datasync.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.datasync.api.pojo.request.FlatMessage;
import com.xyy.saas.datasync.spi.DataSyncIgnoreAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class DataSyncIgnoreAdapterRemote {
    private static final Logger logger = LoggerFactory.getLogger(DataSyncIgnoreAdapterRemote.class);

    @Reference(version = "0.0.1")
    private DataSyncIgnoreAdapter dataSyncIgnoreAdapter;

    /**
     * 判断消息是否需要忽略处理
     * @param fm
     * @return
     */
    public boolean ignoreMatch(FlatMessage fm) {
        if (fm == null || dataSyncIgnoreAdapter == null) {
            return false;
        }
        // 由医保云提供实现 决定是否忽略消息
        return dataSyncIgnoreAdapter.ignoreMatch(fm);
    }
}
