package com.xyy.saas.datasync.mq;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.clinic.center.mqtt.api.CommonMqttMsgPushApi;
import com.xyy.saas.clinic.center.mqtt.dto.req.Push2OrgReq;
import com.xyy.saas.clinic.center.mqtt.enums.AppTypeEnum;
import com.xyy.saas.clinic.center.mqtt.enums.BizLineEnum;
import com.xyy.saas.datasync.common.Constant;
import com.xyy.saas.datasync.pojo.param.SendNoticeParam;
import com.xyy.saas.mqtt.api.MqttApi;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class MQTTProvider {
    private static final String MESSAGE_TYPE = "data_sync";

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;
    @Reference(version = "0.0.1")
    private CommonMqttMsgPushApi commonMqttMsgPushApi;
    @Reference(version = "0.0.1")
    private MqttApi mqttApi;

    /**
     * 广播
     */
    public void sendMulticast(String bizLine, String tableName, List<String> tableNameList) {
        if (BizLineEnum.SAAS.getBizLine().equals(bizLine)) {
            sendMulticastSaaS(bizLine, tableName, tableNameList);
        } else if (BizLineEnum.MEDICAL.getBizLine().equals(bizLine)) {
            sendMulticastMedical(bizLine, tableName, tableNameList);
        } else if (Constant.BIZLINE_MEDICARE.equals(bizLine)) {
            sendMulticastMedicare(bizLine, tableName, tableNameList);
        } else {
            throw new UnsupportedOperationException("MQTT发送失败，暂不支持该业务线[" + bizLine + "]");
        }
    }

    /**
     * 根据机构发送
     */
    public void sendByOrganSign(String bizLine, Set<String> organSigns, String tableName, List<String> tableNameList) {
        if (CollectionUtils.isEmpty(organSigns)) {
            return;
        }
        if(BizLineEnum.SAAS.getBizLine().equals(bizLine)) {
            sendByOrganSignSaaS(bizLine, organSigns, tableName, tableNameList);
        } else if(BizLineEnum.MEDICAL.getBizLine().equals(bizLine)) {
            sendByOrganSignMedical(bizLine, organSigns, tableName, tableNameList);
        } else if (Constant.BIZLINE_MEDICARE.equals(bizLine)) {
            sendByOrganSignMedicare(bizLine, organSigns, tableName, tableNameList);
        } else {
            throw new UnsupportedOperationException("MQTT发送失败，暂不支持该业务线[" + bizLine + "]");
        }
    }

    /**
     * 药店-广播
     * @param bizLine
     * @param tableName
     */
    private void sendMulticastSaaS(String bizLine, String tableName, List<String> tableNameList) {
        if(StringUtils.isEmpty(tableName)) {
            log.error("药店-广播MQTT广播通知失败，消息为空，tableName={}", tableName);
            return;
        }

        String param = JSONObject.toJSONString(SendNoticeParam.build(bizLine, null, tableName, tableNameList));

        if(!messagePushApi.sendMulticastMsg(param)) {
            log.error("药店-广播MQTT广播通知失败，param={}", param);
        }
    }

    /**
     * 药店-机构
     * @param bizLine
     * @param organSigns
     * @param tableName
     */
    public void sendByOrganSignSaaS(String bizLine, Set<String> organSigns, String tableName, List<String> tableNameList) {
        if(CollectionUtils.isEmpty(organSigns) || StringUtils.isEmpty(tableName)) {
            log.error("药店-机构MQTT消息通知失败，消息为空，organSigns={}, tableName={}", JSONObject.toJSONString(organSigns), tableName);
            return;
        }

        organSigns.forEach(organSign -> {
            String param = JSONObject.toJSONString(SendNoticeParam.build(bizLine, organSign, tableName, tableNameList));

            if(!messagePushApi.sendMsgByOrganSign(organSign, param)) {
                log.error("药店-机构MQTT消息通知失败，organSign={}, param={}", organSign, param);
            }
        });
    }

    /**
     * 医保-广播
     * @param bizLine
     * @param tableName
     */
    private void sendMulticastMedical(String bizLine, String tableName, List<String> tableNameList) {
        throw new UnsupportedOperationException("医保暂不支持广播");
    }

    /**
     * 医保-机构
     */
    private void sendByOrganSignMedical(String bizLine, Set<String> organSigns, String tableName, List<String> tableNameList) {
        organSigns.forEach(organSign -> {
            try {
                Push2OrgReq push2OrgReq = new Push2OrgReq();
                push2OrgReq.setBizLine(BizLineEnum.MEDICAL);
                /**
                 * TODO Type后期需要支持配置
                 */
                push2OrgReq.setAppType(AppTypeEnum.SAAS_MEDICAL);
                push2OrgReq.setOrganSign(organSign);
                push2OrgReq.setMessageType(MESSAGE_TYPE);
                // TODO: 2020/12/14 去baseVersion时新增了tableNameList,此处没有做适配,待沟通
                push2OrgReq.setData(tableName);
                commonMqttMsgPushApi.push2Org(push2OrgReq);
            }
            catch(Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        });
    }

    /**
     * 新医保-广播
     * @param bizLine
     * @param tableName
     * @param tableNameList
     */
    private void sendMulticastMedicare(String bizLine, String tableName, List<String> tableNameList){
        if(StringUtils.isEmpty(tableName)) {
            log.error("新医保-广播MQTT广播通知失败，消息为空，tableName={}", tableName);
            return;
        }

        if (!CollectionUtils.isEmpty(tableNameList)) {
            StringBuilder tableNameBuilder = new StringBuilder(tableName);
            for (String table : tableNameList) {
                if (!tableName.equals(table)) {
                    tableNameBuilder.append(",").append(table);
                }
            }
            tableName = tableNameBuilder.toString();
        }

        if(!mqttApi.sendDataSyncMessage(bizLine, tableName)) {
            log.error("新医保-广播MQTT广播通知失败，bizLine={},tables={}", bizLine, tableName);
        }
    }

    /**
     * 新医保-机构
     * @param bizLine
     * @param organSigns
     * @param tableName
     * @param tableNameList
     */
    private void sendByOrganSignMedicare(String bizLine, Set<String> organSigns, String tableName, List<String> tableNameList){
        if(CollectionUtils.isEmpty(organSigns) || StringUtils.isEmpty(tableName)) {
            log.error("新医保-机构MQTT消息通知失败，消息为空，organSigns={}, tableName={}", JSONObject.toJSONString(organSigns), tableName);
            return;
        }

        if (!CollectionUtils.isEmpty(tableNameList)) {
            StringBuilder tableNameBuilder = new StringBuilder(tableName);
            for (String table : tableNameList) {
                if (!tableName.equals(table)) {
                    tableNameBuilder.append(",").append(table);
                }
            }
            tableName = tableNameBuilder.toString();
        }

        for (String organSign : organSigns) {
            if(!mqttApi.sendDataSyncMessage(bizLine, organSign, tableName)) {
                log.error("新医保-机构MQTT消息通知失败，organSign={}, table={}", organSign, tableName);
            }
        }
    }
}
