package com.xyy.saas.datasync.enums;

public enum NoticeTypeEnum {
    DATA_SYNC("data_sync", "数据同步"),
    UPLOAD_DB("upload_db", "上传db"),
    UPLOAD_LOG("upload_log", "上传日志"),
    ;

    private String type;
    private String desc;

    NoticeTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
