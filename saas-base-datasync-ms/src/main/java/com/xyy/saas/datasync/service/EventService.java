package com.xyy.saas.datasync.service;

import com.xyy.saas.datasync.enums.ExceptionCodeEnum;
import com.xyy.saas.datasync.mapper.EventMapper;
import com.xyy.saas.datasync.pojo.EventDo;
import com.xyy.saas.datasync.pojo.param.EventDoParam;
import com.xyy.saas.datasync.rpc.clrtc.ClearConst;
import com.xyy.saas.datasync.rpc.clrtc.ClearDto;
import com.xyy.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class EventService {
    @Resource
    private EventMapper eventMapper;

    /**
     * 批量添加事件
     * @param eventDoList
     * @return
     */
    public void batchInsert(List<EventDo> eventDoList) {
        if (CollectionUtils.isEmpty(eventDoList)) {
            return;
        }
        eventMapper.batchInsert(eventDoList);
    }

    /**
     * 查询count
     * @param param
     * @return
     */
    public int count(EventDoParam param) {
        return eventMapper.count(param);
    }

    /**
     * 删除事件
     * @param ids
     * @return
     */
    public int delete(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return NumberUtils.INTEGER_ZERO;
        }

        return eventMapper.delete(ids);
    }

    /**
     * 数据清除
     * @param clearDto
     * @return
     */
    public int clearOld(ClearDto clearDto, boolean byStep) {
        String bizLine = clearDto.getBizLine();
        String organSign = clearDto.getOrganSign();
        String targetTable = clearDto.getTargetTable();
        Long currentVersion = clearDto.getCurrentVersion();
        return byStep ? this.deleteOldStep(bizLine, organSign, targetTable, currentVersion)
                : this.deleteOldOnce(bizLine, organSign, targetTable, currentVersion);
    }

    /**
     * 删除 id < currentId 的事件
     * @param currentId
     * @return
     */
    private int deleteOldStep(String bizLine, String organSign, String targetTable, Long currentId) {
        if (StringUtil.isEmpty(bizLine, organSign, targetTable) || currentId == null) {
            return 0;
        }
        int deleteRowCount, sum = 0;
        do {
            deleteRowCount = eventMapper.deleteOld(bizLine, organSign, targetTable, currentId, ClearConst.DELETE_LIMIT);
            sum += deleteRowCount;
        } while (deleteRowCount >= ClearConst.DELETE_LIMIT);
        return sum;
    }
    private int deleteOldOnce(String bizLine, String organSign, String targetTable, Long currentId) {
        if (StringUtil.isEmpty(bizLine, organSign, targetTable) || currentId == null) {
            return 0;
        }
        return eventMapper.deleteOld(bizLine, organSign, targetTable, currentId, null);
    }

    /**
     * 获取最大Id
     * @param bizLine
     * @param organSign
     * @param targetTable
     * @return
     */
    public Long getMaxId(String bizLine, String organSign, String targetTable) {
        EventDo eventDo = new EventDo();
        eventDo.setBizLine(bizLine);
        eventDo.setOrganSign(organSign);
        eventDo.setTargetTable(targetTable);
        return Optional.ofNullable(eventMapper.getMaxEvent(eventDo)).map(EventDo::getId).orElse(null);
    }

    /**
     * 分页查询
     * @param param
     * @return
     */
    public List<EventDo> list(EventDoParam param) {
        return eventMapper.list(param);
    }

    /**
     * 分页查询ID
     * @param param
     * @return
     */
    public List<Long> listIdByCondition(EventDoParam param) {
        return eventMapper.listIdByCondition(param);
    }

    /**
     * 根据ID查询实体
     * @param idList
     * @return
     */
    public List<EventDo> listByIds(List<Long> idList) {
        return eventMapper.listByIds(idList);
    }

    public EventDo getMaxEvent(String bizLine, String organSign, String targetTable) {
        if(StringUtils.isEmpty(bizLine) || StringUtils.isEmpty(organSign) || StringUtils.isEmpty(targetTable)) {
            throw new RuntimeException(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        EventDo eventDo = new EventDo();
        eventDo.setBizLine(bizLine);
        eventDo.setOrganSign(organSign);
        eventDo.setTargetTable(targetTable);
        return eventMapper.getMaxEvent(eventDo);
    }

    /**
     * 删除几个月前的旧数据
     * @param month 前几个月
     * @param pageSize 每页删除数据量
     */
    public void deleteOldEvent(int month, int pageSize){
        log.info("删除event表旧数据,入参:month = {}, pageSize = {}", month, pageSize);
        Date currentDate = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(currentDate);
        c.add(Calendar.MONTH, - month);
        Long maxId = eventMapper.getMaxIdByCreateTime(c.getTime());
        int deleteCount = eventMapper.deleteEvent(maxId, pageSize);
        while (deleteCount > 0) {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            deleteCount = eventMapper.deleteEvent(maxId, pageSize);
        }
    }
}
