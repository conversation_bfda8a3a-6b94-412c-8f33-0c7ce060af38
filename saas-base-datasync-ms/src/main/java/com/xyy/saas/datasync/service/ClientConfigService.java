package com.xyy.saas.datasync.service;

import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.api.client.pojo.ClientConfig;
import com.xyy.saas.datasync.mapper.ClientConfigMapper;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import javax.annotation.Resource;
import java.util.List;

@Service
public class ClientConfigService {
    @Resource
    private ClientConfigMapper clientConfigMapper;

    /**
     * 根据ID查询
     * @param id
     * @return
     */
    public ClientConfig getConfig(Long id) {
        if(id == null) {
            throw new RuntimeException("id is null !!!!!!");
        }

        ClientConfig param = new ClientConfig();
        param.setId(id);
        return getConfig(param);
    }

    /**
     * 根据业务线和外部单号查询配置
     * @param bizLine
     * @param outUniqueId
     * @return
     */
    public ClientConfig getConfig(String bizLine, Long outUniqueId) {
        if(StringUtils.isEmpty(bizLine)) {
            throw new RuntimeException("bizLine is null !!!!!!");
        }

        if(outUniqueId == null) {
            throw new RuntimeException("outUniqueId is null !!!!!!");
        }

        ClientConfig param = new ClientConfig();
        param.setBizLine(bizLine);
        param.setOutUniqueId(outUniqueId);
        return getConfig(param);
    }

    /**
     * 获取全部配置
     * @return
     */
    public List<ClientConfig> getConfig() {
        ClientConfig param = new ClientConfig();
        return clientConfigMapper.list(param);
    }

    private ClientConfig getConfig(ClientConfig param) {
        List<ClientConfig> configs = clientConfigMapper.list(param);

        if(CollectionUtils.isEmpty(configs)) {
            return null;
        }
        else if(configs.size() != NumberUtils.INTEGER_ONE) {
            throw new RuntimeException("Configuration is not unique, param=" + JSONObject.toJSONString(param));
        }

        return configs.get(NumberUtils.INTEGER_ZERO);
    }

    /**
     * 新增/编辑ClientConfig
     * @param clientConfig
     */
    public void upsertClientConfig(ClientConfig clientConfig){
        clientConfigMapper.upsert(clientConfig);
    }
}
