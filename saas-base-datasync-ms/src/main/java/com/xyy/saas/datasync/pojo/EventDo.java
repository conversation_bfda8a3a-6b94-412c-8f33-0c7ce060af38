package com.xyy.saas.datasync.pojo;

import com.xyy.saas.datasync.api.pojo.request.DownloadParam;

import java.util.Date;

/**
 * 事件实体
 */
public class EventDo {
    /**
     * 事件ID/版本号
     */
    private Long id;
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 来源机构号
     */
    private String sourceOrganSign;
    /**
     * 机构号
     */
    private String organSign;
    /**
     * 来源表
     */
    private String sourceTable;
    /**
     * 目标表
     */
    private String targetTable;
    /**
     * 来源表ID
     */
    private Long sourceTableId;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 当前业务数据更新时间
     */
    private Date bizDataUpdateTime;
    /**
     * 事件创建时间
     */
    private Date createTime;

    /**
     * 构建对象
     * @param bizLine
     * @param organSign
     * @param sourceTable
     * @param targetTable
     * @param sourceTableId
     * @param eventType
     * @param bizDataUpdateTime
     * @return
     */
    public static EventDo build(String bizLine, String sourceOrganSign, String organSign, String sourceTable,
                                String targetTable, Long sourceTableId, String eventType, Date bizDataUpdateTime)
    {
        EventDo eventDo = new EventDo();
        eventDo.setBizLine(bizLine);
        eventDo.setSourceOrganSign(sourceOrganSign);
        eventDo.setOrganSign(organSign);
        eventDo.setSourceTable(sourceTable);
        eventDo.setTargetTable(targetTable);
        eventDo.setSourceTableId(sourceTableId);
        eventDo.setEventType(eventType);
        eventDo.setBizDataUpdateTime(bizDataUpdateTime);
        return eventDo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getSourceOrganSign() {
        return sourceOrganSign;
    }

    public void setSourceOrganSign(String sourceOrganSign) {
        this.sourceOrganSign = sourceOrganSign;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getSourceTable() {
        return sourceTable;
    }

    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    public String getTargetTable() {
        return targetTable;
    }

    public void setTargetTable(String targetTable) {
        this.targetTable = targetTable;
    }

    public Long getSourceTableId() {
        return sourceTableId;
    }

    public void setSourceTableId(Long sourceTableId) {
        this.sourceTableId = sourceTableId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public Date getBizDataUpdateTime() {
        return bizDataUpdateTime;
    }

    public void setBizDataUpdateTime(Date bizDataUpdateTime) {
        this.bizDataUpdateTime = bizDataUpdateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
