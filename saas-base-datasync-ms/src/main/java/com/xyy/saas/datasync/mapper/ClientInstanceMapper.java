package com.xyy.saas.datasync.mapper;

import com.xyy.saas.datasync.api.client.pojo.ClientInstance;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 客户端实例
 */
@Mapper
public interface ClientInstanceMapper {
    /**
     * 根据条件查询
     * @param clientInstance
     * @return
     */
    List<ClientInstance> list(ClientInstance clientInstance);

    /**
     * 更新实体
     * @param clientInstance
     * @return
     */
    int update(ClientInstance clientInstance);

    void insert(ClientInstance clientInstance);
}
