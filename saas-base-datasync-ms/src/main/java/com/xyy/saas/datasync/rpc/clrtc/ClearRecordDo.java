package com.xyy.saas.datasync.rpc.clrtc;

import lombok.Data;

import java.util.Date;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class ClearRecordDo {
    private String bizLine;
    private String organSign;
    private String targetTable;

    /**
     * 最后一次删除时的version
     */
    private Long lastVersion;
    /**
     * 当前版本号（预计下次删除的version）
     */
    private Long currentVersion;
    /**
     * 最后一次删除数量
     */
    private Long lastDelete;
    /**
     * 累计删除数量
     */
    private Long sumDelete;

    /**
     * 统计剩余行数(数据量 <= {@link ClearConst#STAT_OVER_LIMIT} 的不统计)
     */
    private Long cnt;
    /**
     * 统计最大版本
     */
    private Long maxId;

    /**
     * 是否需要删除 （0 只做统计; 1 可做删除）
     */
    private Integer needClear;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
