package com.xyy.saas.datasync.mapper;

import com.xyy.saas.datasync.rpc.clrtc.ClearRecordDo;
import com.xyy.saas.datasync.rpc.clrtc.ClearStatDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 事件Mapper
 */
@Mapper
public interface ClearRecordMapper {

    /**
     * 添加记录或修改
     *      last_version    = GREATEST(last_version, #{lastVersion}),
     *      current_version = GREATEST(current_version, #{currentVersion}),
     *      last_delete     = #{lastDelete},
     *      sum_delete      = sum_delete + #{lastDelete},
     *      cnt             = #{count},
     *      max_id          = #{maxId},
     *      need_clear      = #{needClear}
     * @param clearRecordDo
     * @return
     */
    int insertOrUpdate(ClearRecordDo clearRecordDo);

    /**
     * 一次性统计
     * @param overLimit
     * @return
     */
    List<ClearStatDo> stat(@Param("overLimit") int overLimit);

    /**
     * 查询需要删除的组合
     * @return
     */
    List<ClearRecordDo> listForClear();
}
