package com.xyy.saas.datasync.pojo.param;

import com.xyy.saas.datasync.pojo.ConfigDo;
import com.xyy.saas.datasync.pojo.EventDo;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class BatchEventParam implements Serializable {
    private ConfigDo configDo;
    private List<EventDo> eventDoList;
    private Set<String> noticeOrganSign;

    /**
     * 构建对象
     * @param configDo
     * @param eventDoList
     * @return
     */
    public static BatchEventParam build(ConfigDo configDo, List<EventDo> eventDoList, Set<String> noticeOrganSign) {
        BatchEventParam param = new BatchEventParam();
        param.setConfigDo(configDo);
        param.setEventDoList(eventDoList);
        param.setNoticeOrganSign(noticeOrganSign);
        return param;
    }

    public ConfigDo getConfigDo() {
        return configDo;
    }

    public void setConfigDo(ConfigDo configDo) {
        this.configDo = configDo;
    }

    public List<EventDo> getEventDoList() {
        return eventDoList;
    }

    public void setEventDoList(List<EventDo> eventDoList) {
        this.eventDoList = eventDoList;
    }

    public Set<String> getNoticeOrganSign() {
        return noticeOrganSign;
    }

    public void setNoticeOrganSign(Set<String> noticeOrganSign) {
        this.noticeOrganSign = noticeOrganSign;
    }
}
