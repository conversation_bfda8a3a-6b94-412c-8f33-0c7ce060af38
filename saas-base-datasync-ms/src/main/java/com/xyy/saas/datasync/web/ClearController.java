package com.xyy.saas.datasync.web;

import com.xyy.saas.datasync.api.enums.CodeEnum;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.rpc.clrtc.ClearDto;
import com.xyy.saas.datasync.rpc.clrtc.ClearManager;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@RestController
@RequestMapping("/clear")
public class ClearController {
    @Resource
    ClearManager clearManager;

    /**
     * 统计    定时任务触发
     * @return
     */
    @PostMapping("/stat")
    public Result<String> stat() {
        String statMsg = clearManager.doStat();
        return new Result<>(CodeEnum.SUCCESS.getCode(), statMsg, null);
    }

    /**
     * 清除    定时任务触发
     * @return
     */
    @PostMapping("/delete")
    public Result<String> delete() {
        String statMsg = clearManager.doDelete();
        return new Result<>(CodeEnum.SUCCESS.getCode(), statMsg, null);
    }

    /**
     * 清除    手动触发
     * @return
     */
    @PostMapping("/force/delete")
    public Result<String> forceDelete(@RequestBody ClearDto dto) {
        String statMsg = clearManager.forceDelete(dto);
        return new Result<>(CodeEnum.SUCCESS.getCode(), statMsg, null);
    }
}
