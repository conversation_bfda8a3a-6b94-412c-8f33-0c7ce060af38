package com.xyy.saas.datasync.pojo.param;

import com.ctrip.framework.apollo.ConfigService;
import com.xyy.saas.datasync.api.pojo.request.DownloadParam;
import com.xyy.saas.datasync.pojo.EventDo;

public class EventDoParam extends EventDo {
    /**
     * 偏移量
     */
    private Integer offset;
    /**
     * 取数范围
     */
    private Integer limit;

    /**
     * 构建对象
     * @param param
     * @return
     */
    public static EventDoParam build(DownloadParam param, Boolean isAllDataSyncType) {
        EventDoParam eventDoParam = new EventDoParam();
        eventDoParam.setBizLine(param.getBizLine());

        // 全量同步，事件表不记录机构号
        if(!Boolean.TRUE.equals(isAllDataSyncType)) {
            eventDoParam.setOrganSign(param.getOrganSign());
        }

        eventDoParam.setTargetTable(param.getTableName());
        eventDoParam.setId(param.getCurrentVersion());
        eventDoParam.setBizDataUpdateTime(param.getUpdateTime());
        Integer pageSize = param.getPageSize() != null ? param.getPageSize() :
                ConfigService.getAppConfig().getIntProperty("download.pageSize", 100);
        eventDoParam.setLimit(pageSize);
        return eventDoParam;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }
}
