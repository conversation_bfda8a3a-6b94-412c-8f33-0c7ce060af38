package com.xyy.saas.datasync.rpc.hyt;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.datasync.api.client.pojo.ClientConfig;
import com.xyy.saas.datasync.api.hyt.HytConfigApi;
import com.xyy.saas.datasync.api.hyt.pojo.HytClientConfig;
import com.xyy.saas.datasync.api.hyt.pojo.HytClientInstance;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.service.ClientConfigService;
import com.xyy.saas.datasync.service.ClientInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/5/11
 */
@Slf4j
@Service(version = "0.0.1")
public class HytConfigApiImpl implements HytConfigApi {
    @Autowired
    private ClientConfigService clientConfigService;

    /**
     * 根据ERP软件ID查询荷叶通ClientConfig
     *
     * @param erpSoftwareId
     * @return
     */
    @Override
    public Result<HytClientConfig> queryErpConfig(Long erpSoftwareId){
        if (erpSoftwareId == null) {
            return Result.fail("荷叶通ERP软件ID不能为空");
        }
        Long clientConfigId = ClientInstanceService.getHytClientConfigId(erpSoftwareId);
        ClientConfig config = clientConfigService.getConfig(clientConfigId);
        if (config == null) {
            return Result.success(null);
        }
        HytClientConfig hytClientConfig = new HytClientConfig();
        hytClientConfig.setSoftwareId(ClientInstanceService.getHytErpSoftwareId(config.getId()));
        hytClientConfig.setSoftwareName(config.getRemark());
        hytClientConfig.setConfigJson(config.getConfig());
        return Result.success(hytClientConfig);
    }

    /**
     * 更新荷叶通ClientConfig
     *
     * @param hytClientConfig
     * @return
     */
    @Override
    public Result<Boolean> upsertConfig(HytClientConfig hytClientConfig){
        try {
            ClientConfig config = new ClientConfig();
            config.setId(ClientInstanceService.getHytClientConfigId(hytClientConfig.getSoftwareId()));
            config.setBizLine("HYT");
            config.setOutUniqueId(config.getId());
            config.setRemark(hytClientConfig.getSoftwareName());
            config.setConfig(hytClientConfig.getConfigJson());
            clientConfigService.upsertClientConfig(config);
        } catch (Exception e) {
            log.error("荷叶通ERP抓取数据ClientConfig编辑异常, request:{}", JSON.toJSONString(hytClientConfig), e);
            return Result.fail(e.getMessage());
        }
        return Result.success(Boolean.TRUE);
    }

    /**
     * 更新荷叶通ClientInstance
     *
     * @param hytClientInstance
     * @return
     */
    @Override
    public Result<Boolean> upsertInstance(HytClientInstance hytClientInstance){
        return null;
    }
}
