package com.xyy.saas.datasync.rpc.clrtc;

import com.xyy.saas.datasync.service.ClearRecordService;
import com.xyy.saas.datasync.utils.TimeUtil;
import com.xyy.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
public class ClearManager {
    @Resource
    ClearRecordService clearRecordService;

    /**
     * event 版本信息
     */
    private static final Map<String,ClearDto> VERSION_MAP = new ConcurrentHashMap<>();

    /** 统计执行状态 */
    private static final AtomicBoolean STATING = new AtomicBoolean(false);

    /**
     * 是否清除历史event
     */
    @Value("${datasync.event.unused.clear.switch:false}")
    boolean clearSwitch;

    /**
     * 不清除历史event的表(表名,用逗号连接. 例: a,b,c)
     */
    @Value("#{'${datasync.event.unused.clear.exclude.tables:}'.toUpperCase().split(',')}")
    private Set<String> clearExcludeTables;

    /**
     * 缓存刷新到db 延迟 单位：ms， 默认5分钟
     */
    @Value("${datasync.event.unused.clear.flush.delay:300000}")
    private int flushDelay;

    /**
     * 判断开关
     *
     * @return
     */
    public boolean getClearSwitch() {
        return this.clearSwitch;
    }

    /**
     * 记录版本信息
     *
     * @param bizLine
     * @param organSign
     * @param targetTable
     * @param currentVersion
     */
    public void refreshCache(String bizLine, String organSign, String targetTable, Long currentVersion) {
        if (StringUtil.isEmpty(bizLine, organSign, targetTable) || currentVersion == null) {
            return;
        }
        if (clearExcludeTables.contains(targetTable.toUpperCase())) {
            return;
        }
        final String key = bizLine + organSign + targetTable;
        // 删除无用数据 id < currentVersion 的event记录
        ClearDto old = VERSION_MAP.get(key);
        if (old == null || old.getCurrentVersion().compareTo(currentVersion) < 0) {
            // 保存当前最新版本 -> 缓存
            ClearDto clearDto = old != null ? old : new ClearDto(bizLine, organSign, targetTable);
            clearDto.setCurrentVersion(currentVersion);
            clearDto.setEarliestAt(System.currentTimeMillis());
            clearDto.setFlushToDb(false);
            VERSION_MAP.put(key, clearDto);
            return;
        }
        // 刷新缓存 -> db
        if (!old.isFlushToDb() && System.currentTimeMillis() - old.getEarliestAt() > flushDelay) {
            try {
                clearRecordService.flushCacheToDb(old);
                old.setFlushToDb(true);
            } catch (Throwable e) {
                log.error("[数据 记录] 缓存刷新执行异常: {}", e.getMessage(), e);
            }
        }
    }


    /**
     * 统计
     *  1. 单例执行
     *  2. 允许执行时间段：00:00 ~ 06:00
     *  3. 统计结果保存到数据库
     * @return
     */
    public String doStat() {
        if (!STATING.compareAndSet(false, true)) {
            return "正在统计中";
        }
        // 限制监测统计时间段
        LocalTime now = LocalTime.now();
        if (now.isBefore(ClearConst.STAT_TIME_FRAME[0]) || now.isAfter(ClearConst.STAT_TIME_FRAME[1])) {
            return MessageFormat.format("当前不允许操作，请在 {0} ～ {1} 时间段内尝试",
                                        ClearConst.STAT_TIME_FRAME[0].toString(),
                                        ClearConst.STAT_TIME_FRAME[1].toString());
        }
        long begin = System.currentTimeMillis();
        log.info("[数据 统计] 开始统计...");
        try {
            List<ClearStatDo> statList = clearRecordService.stat(ClearConst.STAT_OVER_LIMIT);
            statList.forEach(clearRecordService::statAndRecord);
        } catch (Throwable e) {
            log.error("[数据 统计] 统计执行异常: {}", e.getMessage(), e);
            return MessageFormat.format("统计执行异常: {0}, 堆栈: {1}", e.getMessage(), ExceptionUtils.getStackTrace(e));
        } finally {
            STATING.set(false);
        }
        String costToStr = TimeUtil.costToStr(System.currentTimeMillis() - begin);
        log.info("[数据 统计] 统计结束, 耗时: {}", costToStr);
        return "统计完成, 耗时: " + costToStr;
    }

    /**
     * 执行清除
     */
    public String doDelete() {
        long begin = System.currentTimeMillis();
        log.info("[数据 清除] 开始清理...");
        int deleteCount = 0;
        try {
            List<ClearRecordDo> clearList = clearRecordService.listForClear();
            // 不开异步线程执行，防止数据库锁竞争
            // 数据量大的放在后面删除
            deleteCount = clearList.stream().sorted(Comparator.comparing(l -> Optional.ofNullable(l.getCnt()).orElse(0L)))
                                   .mapToInt(clearRecordService::clearAndRecord).sum();
        } catch (Throwable e) {
            log.error("[数据 清除] 清理执行异常: {}", e.getMessage(), e);
            return MessageFormat.format("清理执行异常: {0}, 堆栈: {1}", e.getMessage(), ExceptionUtils.getStackTrace(e));
        }
        String costToStr = TimeUtil.costToStr(System.currentTimeMillis() - begin);
        log.info("[数据 清除] 清理结束, 清除总数: {}, 耗时: {}", deleteCount, costToStr);
        return "清理完成, 总数:" + deleteCount + ", 耗时: " + costToStr;
    }

    /**
     * 手动强制清除
     */
    public String forceDelete(ClearDto dto) {
        if (dto == null || StringUtil.isEmpty(dto.getBizLine(), dto.getOrganSign(), dto.getTargetTable()) || dto.getCurrentVersion() == null) {
            return "参数错误";
        }
        if (clearExcludeTables.contains(dto.getTargetTable().toUpperCase())) {
            return "表不在清除范围";
        }
        long begin = System.currentTimeMillis();
        log.info("[数据 清除] 开始强制清理...");
        int deleteCount = 0;
        try {
            ClearRecordDo clearRecordDo = new ClearRecordDo();
            clearRecordDo.setBizLine(dto.getBizLine());
            clearRecordDo.setOrganSign(dto.getOrganSign());
            clearRecordDo.setTargetTable(dto.getTargetTable());
            clearRecordDo.setCurrentVersion(dto.getCurrentVersion());
            deleteCount = clearRecordService.clearAndRecord(clearRecordDo);
        } catch (Throwable e) {
            log.error("[数据 清除] 清理执行异常: {}", e.getMessage(), e);
            return MessageFormat.format("清理执行异常: {0}, 堆栈: {1}", e.getMessage(), ExceptionUtils.getStackTrace(e));
        }
        String costToStr = TimeUtil.costToStr(System.currentTimeMillis() - begin);
        log.info("[数据 清除] 清理结束, 清除总数: {}, 耗时: {}", deleteCount, costToStr);
        return "清理完成, 总数:" + deleteCount + ", 耗时: " + costToStr;
    }
}
