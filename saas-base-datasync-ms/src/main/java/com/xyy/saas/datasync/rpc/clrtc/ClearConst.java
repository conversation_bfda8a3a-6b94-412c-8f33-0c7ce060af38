package com.xyy.saas.datasync.rpc.clrtc;

import java.time.LocalTime;

/**
 * 常量
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface ClearConst {
    /**
     * 每次删除限制最大删除行数
     */
    int DELETE_LIMIT = 500;

    /**
     * 监测超出限制量级的历史数据, 默认最低限制
     */
    int STAT_OVER_LIMIT = 500;

    /**
     * 监测统计时间段, 默认 00:00 - 06:00
     */
    LocalTime[] STAT_TIME_FRAME = {LocalTime.ofSecondOfDay(0), LocalTime.ofSecondOfDay(20 * 3600)};
}
