package com.xyy.saas.datasync.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据同步类型
 */
@Getter
@AllArgsConstructor
public enum DataSyncTypeEnum {
    SINGLE(1, "一对一"),
    MANY(2, "一对多"),
    ALL(3, "全量"),
    ;

    /**
     * 根据type获取枚举
     * @param type
     * @return
     */
    public static DataSyncTypeEnum get(Integer type) {
        for(DataSyncTypeEnum val : values()) {
            if(val.type.equals(type)) {
                return val;
            }
        }

        return null;
    }

    private Integer type;
    private String desc;
}
