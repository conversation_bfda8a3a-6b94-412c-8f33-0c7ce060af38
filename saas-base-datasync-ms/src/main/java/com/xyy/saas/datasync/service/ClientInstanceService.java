package com.xyy.saas.datasync.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.api.client.pojo.ClientConfig;
import com.xyy.saas.datasync.api.client.pojo.ClientInstance;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.mapper.ClientInstanceMapper;
import com.xyy.saas.yzh.datasync.dto.ErpInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.util.*;

@Service
public class ClientInstanceService {
    private static final Logger logger = LoggerFactory.getLogger(ClientInstanceService.class);

    @Resource
    private ClientInstanceMapper clientInstanceMapper;
    @Resource
    private ClientConfigService clientConfigService;

    /**
     * 查询实例
     * @param clientId
     * @param clientKey
     * @return
     */
    public ClientInstance getClientInstance(Long clientId, String clientKey) {
        if(clientId == null) {
            throw new RuntimeException("clientId is null !!!!!");
        }
        if(StringUtils.isEmpty(clientKey)) {
            throw new RuntimeException("clientKey is null !!!!!");
        }

        ClientInstance param = new ClientInstance();
        param.setClientId(clientId);
        param.setClientKey(clientKey);
        ClientInstance clientInstance = getClientInstance0(param);

        if(clientInstance == null) {
            return null;
        }

        ClientConfig clientConfig = clientConfigService.getConfig(clientInstance.getClientConfigId());
        // merge config
        clientInstance.setInstanceConfig(mergeConfig(clientInstance.getInstanceConfig(), clientConfig.getConfig()));
        return clientInstance;
    }

    /**
     * 查询实例
     * @param bizLine
     * @param outUniqueId
     * @return
     */
    public ClientInstance getInstance(String bizLine, Long outUniqueId) {
        if(StringUtils.isEmpty(bizLine)) {
            throw new RuntimeException("bizLine is null !!!!!");
        }
        if(outUniqueId == null) {
            throw new RuntimeException("outUniqueId is null !!!!!");
        }

        ClientInstance param = new ClientInstance();
        param.setBizLine(bizLine);
        param.setOutUniqueId(outUniqueId);
        return getClientInstance0(param);
    }

    /**
     * 更新任务参数
     * @param bizLine
     * @param outUniqueId
     * @param taskId
     * @param param
     */
    public void updateTaskInfo(String bizLine, Long outUniqueId, Integer taskId, Map<String, Object> param) {
        if(CollectionUtils.isEmpty(param)) {
            logger.error("更新任务属性失败，参数为空");
            return;
        }

        ClientInstance instance = getInstance(bizLine, outUniqueId);

        if(instance == null) {
            logger.error("更新任务属性失败，客户端实例不存在，bizLine={}, outUniqueId={}, taskId={}, param={}", bizLine, outUniqueId, taskId, param);
            return;
        }

        JSONArray instanceConfigArray = JSONObject.parseArray(instance.getInstanceConfig());
        JSONObject task = getTask(taskId, instanceConfigArray);

        if(task == null) {
            task = new JSONObject();
            task.put("taskId", taskId);
            instanceConfigArray.add(task);
        }

        for(Map.Entry<String, Object> entry : param.entrySet()) {
            task.put(entry.getKey(), entry.getValue());
        }

        instance.setInstanceConfig(instanceConfigArray.toJSONString());
        clientInstanceMapper.update(instance);
    }

    /**
     * 查询实例
     * @param param
     * @return
     */
    private ClientInstance getClientInstance0(ClientInstance param) {
        List<ClientInstance> clientInstances = clientInstanceMapper.list(param);

        if(CollectionUtils.isEmpty(clientInstances)) {
            return null;
        }
        else if(clientInstances.size() != NumberUtils.INTEGER_ONE) {
            throw new RuntimeException("Configuration is not unique, param=" + JSONObject.toJSONString(param));
        }

        return clientInstances.get(NumberUtils.INTEGER_ZERO);
    }

    /**
     * 合并配置
     * @param instanceConfig
     * @param config
     * @return
     * https://wiki.int.ybm100.com/pages/viewpage.action?pageId=379534459
     * 游标优先级 rollbackOffset > currentOffset > offset
     */
    private String mergeConfig(String instanceConfig, String config) {
        JSONArray instanceConfigArray = JSONObject.parseArray(instanceConfig);
        JSONObject configObj = JSONObject.parseObject(config);
        JSONArray taskArray = configObj.getJSONArray("tasks");

        for(int i = 0; i < taskArray.size(); i++) {
            JSONObject obj = taskArray.getJSONObject(i);
            Integer taskId = obj.getInteger("taskId");

            Integer interval = getTaskInterval(taskId, instanceConfigArray);
            String cursorOffset = getTaskOffset(taskId, instanceConfigArray);
            Integer pageSize = getTaskPageSize(taskId, instanceConfigArray);
            Boolean firstRun = getFirstRunTag(taskId, instanceConfigArray);

            if(interval != null) {
                obj.put("interval", interval);
            }

            if(cursorOffset != null) {
                obj.put("cursorOffset", cursorOffset);
            }

            if(pageSize != null) {
                obj.put("pageSize", pageSize);
            }

            if(firstRun != null) {
                obj.put("firstRun", firstRun);
            }
        }

        return configObj.toJSONString();
    }

    /**
     * 执行间隔
     * @param taskId
     * @param target
     * @return
     */
    private Integer getTaskInterval(Integer taskId, JSONArray target) {
        if(taskId == null || target == null || target.size() == NumberUtils.INTEGER_ZERO) {
            return null;
        }

        JSONObject obj = getTask(taskId, target);

        if(obj == null) {
            return null;
        }

        return obj.getInteger("interval");
    }

    /**
     * 页大小
     * @param taskId
     * @param target
     * @return
     */
    private Integer getTaskPageSize(Integer taskId, JSONArray target) {
        if(taskId == null || target == null || target.size() == NumberUtils.INTEGER_ZERO) {
            return null;
        }

        JSONObject obj = getTask(taskId, target);

        if(obj == null) {
            return null;
        }

        return obj.getInteger("pageSize");
    }

    /**
     * 首次执行
     * @param taskId
     * @param target
     * @return
     */
    private Boolean getFirstRunTag(Integer taskId, JSONArray target) {
        if(taskId == null || target == null || target.size() == NumberUtils.INTEGER_ZERO) {
            return null;
        }

        JSONObject obj = getTask(taskId, target);

        if(obj == null) {
            return null;
        }

        return obj.getBoolean("firstRun");
    }

    /**
     * 偏移量
     * @param taskId
     * @param target
     * @return
     * 游标优先级 rollbackOffset > currentOffset > offset
     */
    private String getTaskOffset(Integer taskId, JSONArray target) {
        if(taskId == null || target == null || target.size() == NumberUtils.INTEGER_ZERO) {
            return null;
        }

        JSONObject obj = getTask(taskId, target);

        if(obj == null) {
            return null;
        }

        String rollbackOffset = obj.getString("rollbackOffset");

        if(!StringUtils.isEmpty(rollbackOffset)) {
            return rollbackOffset;
        }

        String currentOffset = obj.getString("currentOffset");

        if(!StringUtils.isEmpty(currentOffset)) {
            return currentOffset;
        }

        return obj.getString("offset");
    }

    /**
     * 获取任务
     * @param taskId
     * @param target
     * @return
     */
    private JSONObject getTask(Integer taskId, JSONArray target) {
        for(int i = 0; i < target.size(); i++) {
            JSONObject obj = target.getJSONObject(i);

            if(Objects.equals(taskId, obj.getInteger("taskId"))) {
                return obj;
            }
        }

        return null;
    }

    /**
     * 荷叶通-根据ERP软件ID生成ClientConfigId
     * @param erpSoftwareId
     * @return
     */
    public static Long getHytClientConfigId(Long erpSoftwareId){
        return erpSoftwareId + 10000000L;
    }

    /**
     * 荷叶通-根据ERP软件ID生成ClientConfigId
     * @param hytClientConfigId
     * @return
     */
    public static Long getHytErpSoftwareId(Long hytClientConfigId){
        return hytClientConfigId - 10000000L;
    }

    /**
     * 根据荷叶通中查询到的商户绑定ERP信息插入爬虫实例
     * @param erpInfo
     * @return
     */
    public Result<Boolean> insertHytClientInstance(ErpInfoDto erpInfo){
        Long clientConfigId = getHytClientConfigId(erpInfo.getSoftwareId());
        ClientConfig config = clientConfigService.getConfig(clientConfigId);
        if (config == null) {
            return Result.fail("未查询到当前ERP软件版本的ClientConfig配置信息,ID:" + clientConfigId);
        }
        long clientId = this.convertClientIdByErpInfo(erpInfo);
        String clientKey = this.convertClientKeyByErpInfo(erpInfo);

        ClientInstance instance = new ClientInstance();
        instance.setBizLine("HYT");
        instance.setOutUniqueId(clientId);
        instance.setClientId(clientId);
        instance.setClientKey(clientKey);
        instance.setClientType("PLUGIN");
        instance.setClientConfigId(clientConfigId);
        instance.setInstanceConfig(this.initHytInstanceConfig(config));
        clientInstanceMapper.insert(instance);
        return Result.success(Boolean.TRUE);
    }

    /**
     * 根据爬虫配置初始化爬虫实例
     * @param config
     * @return
     */
    private String initHytInstanceConfig(ClientConfig config){
        List<Map<String, Object>> initTaskList = new ArrayList<>();
        try {
            String configStr = config.getConfig();
            JSONObject configJson = JSON.parseObject(configStr);
            JSONArray tasksJSONArray = (JSONArray) configJson.get("tasks");
            for (int i = 0; i < tasksJSONArray.size(); i++) {
                Map<String, Object> taskMap = new LinkedHashMap<>();
                JSONObject task = tasksJSONArray.getJSONObject(i);
                Object taskId = task.get("taskId");
                taskMap.put("taskId", taskId);
                initTaskList.add(taskMap);
            }
        } catch (Exception e) {
            logger.error("根据ClientConfig初始化实例异常, config:{}", JSON.toJSONString(config), e);
        }
        return JSON.toJSONString(initTaskList);
    }

    /**
     * 转换ClientId
     * @param erpInfo
     * @return
     */
    public long convertClientIdByErpInfo(ErpInfoDto erpInfo){
        return Long.parseLong(erpInfo.getMerchantId() + "00" + erpInfo.getErpRelationId());
    }

    /**
     * 转换ClientKey
     * @param erpInfo
     * @return
     */
    public String convertClientKeyByErpInfo(ErpInfoDto erpInfo){
        return erpInfo.getMerchantId() + "_hyt_" + erpInfo.getErpRelationId();
    }
}
