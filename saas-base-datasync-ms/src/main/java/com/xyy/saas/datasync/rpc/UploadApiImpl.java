package com.xyy.saas.datasync.rpc;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.api.UploadApi;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.api.pojo.request.UploadData;
import com.xyy.saas.datasync.api.pojo.request.UploadParam;
import com.xyy.saas.datasync.config.UploadConfigManager;
import com.xyy.saas.datasync.enums.ExceptionCodeEnum;
import com.xyy.saas.datasync.mq.UploadProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;
import java.util.List;

@Service(version = "0.0.1")
@SuppressWarnings("unchecked")
public class UploadApiImpl implements UploadApi {
    private static final Logger logger = LoggerFactory.getLogger(UploadApiImpl.class);

    @Resource
    private UploadProvider uploadProvider;
    @Resource
    private UploadConfigManager uploadConfigManager;

    /**
     * 数据上传
     *
     * @param param
     * @return
     */
    @Override
    public Result pushData(UploadParam param) {
        // 1.参数校验
        if(param == null) {
            return Result.fail(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        param.check();

        // 2.数据发送
        List<UploadData> items = param.getData();

        items.forEach(item -> {
            String key = param.getBizLine() + "-" + item.getTableName();
            String topic = uploadConfigManager.getTopic(key);
            uploadProvider.send(key, topic, item.getData());
        });

        return Result.success();
    }
}
