package com.xyy.saas.datasync.utils;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.datasync.enums.EventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 业务相关工具类
 */
@Slf4j
public class BizUtil {
    private static final Logger logger = LoggerFactory.getLogger(BizUtil.class);

    public static ThreadLocal<SimpleDateFormat> threadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    /**
     * 获取主键ID
     * @param values
     * @return
     */
    public static Long getId(Map<String, String> values) {
        return Long.valueOf(values.get("id"));
    }

    /**
     * 获取业务更新时间
     * @return
     */
    public static Date getBizDataUpdateTime(String eventType, Map<String, String> values) {
        String strData;

        if(EventTypeEnum.INSERT.getType().equals(eventType)) {
            strData = values.get("create_time");
        }
        else {
            strData = values.get("update_time");
        }

        try {
            SimpleDateFormat sdf = threadLocal.get();
            return sdf.parse(strData);
        }
        catch(Exception ex) {
            // 获取业务更新时间失败，降级成系统当前时间
            logger.error(ex.getMessage(), ex);
            return new Date();
        }
    }

    /**
     * 机构号字段属性名
     */
    private static final String[] ORGAN_SIGN_FIELD_NAMES = {"organ_sign", "organSign", "OrganSign", "organsign"};

    /**
     * 从Object中获取机构号(浅度获取)
     * @param obj 操作对象
     * @return
     */
    public static String getOrganSignInObject(Object obj){
        if (obj instanceof Map) {
            Map map = (Map) obj;
            for (String organSignFieldName : ORGAN_SIGN_FIELD_NAMES) {
                if (map.containsKey(organSignFieldName)) {
                    return (String)map.get(organSignFieldName);
                }
            }
            log.error("Object类型为Map,获取机构号失败,Object:{}", JSON.toJSONString(obj));
            return null;
        }
        Class<?> objClass = obj.getClass();
        for (String organSignFieldName : ORGAN_SIGN_FIELD_NAMES) {
            try {
                Field organSign = objClass.getDeclaredField(organSignFieldName);
                organSign.setAccessible(true);
                return (String)organSign.get(obj);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.error("从Object中使用{}获取机构号失败", organSignFieldName, e);
            }
        }
        log.error("从Object中获取机构号失败,Object:{}", JSON.toJSONString(obj));
        return null;
    }

    /**
     * 向Object设置机构号(浅度设置)
     * @param obj
     * @param newOrganSign
     * @return
     */
    public static boolean setOrganSignInObject(Object obj, String newOrganSign){
        if (obj instanceof Map) {
            Map map = (Map) obj;
            for (String organSignFieldName : ORGAN_SIGN_FIELD_NAMES) {
                if (map.containsKey(organSignFieldName)) {
                    map.put(organSignFieldName, newOrganSign);
                    return Boolean.TRUE;
                }
            }
            log.error("Object类型为Map,设置机构号失败,Object:{}", JSON.toJSONString(obj));
            return Boolean.FALSE;
        }

        Class<?> objClass = obj.getClass();
        for (String organSignFieldName : ORGAN_SIGN_FIELD_NAMES) {
            try {
                Field organSign = objClass.getDeclaredField(organSignFieldName);
                organSign.setAccessible(true);
                organSign.set(obj, newOrganSign);
                return Boolean.TRUE;
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.error("向Object使用{}设置机构号失败", organSignFieldName, e);
            }
        }
        log.error("向Object设置机构号失败,Object:{}", JSON.toJSONString(obj));
        return Boolean.FALSE;
    }
}
