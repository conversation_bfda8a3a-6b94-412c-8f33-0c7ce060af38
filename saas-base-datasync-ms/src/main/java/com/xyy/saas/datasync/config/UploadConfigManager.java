package com.xyy.saas.datasync.config;

import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.api.client.ClientConstant;
import com.xyy.saas.datasync.api.client.pojo.ClientConfig;
import com.xyy.saas.datasync.enums.ConfigTypeEnum;
import com.xyy.saas.datasync.pojo.ConfigDo;
import com.xyy.saas.datasync.pojo.dto.UploadExtend;
import com.xyy.saas.datasync.service.ClientConfigService;
import com.xyy.saas.datasync.service.ConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class UploadConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(UploadConfigManager.class);

    private Map<String, MQInfo> mqs = new ConcurrentHashMap<>();

    @Resource
    private ConfigService configService;
    @Resource
    private ClientConfigService clientConfigService;

    /**
     * 获取MQInfo
     * @return
     */
    public Map<String, MQInfo> getMqs() {
        return mqs;
    }

    /**
     * 根据key获取topic
     * @param key
     * @return
     */
    public String getTopic(String key) {
        MQInfo mqInfo = mqs.get(key);
        return mqInfo == null ? null : mqInfo.getTopic();
    }

    public void load(List<ConfigDo> configs) {
        if(CollectionUtils.isEmpty(configs)) {
            return;
        }

        configs.forEach(config -> {
            UploadExtend uploadExtend = JSONObject.parseObject(config.getExtend(), UploadExtend.class);

            if(uploadExtend != null) {
                mqs.put(config.getBizLine() + "-" + config.getSourceTable(),
                        MQInfo.build(uploadExtend.getTargetGroup(), uploadExtend.getTargetTopic(), config.getBizLine()));
            }
        });

        logger.info("配置加载完成，configs={}", JSONObject.toJSONString(configs));
    }

    @PostConstruct
    private void init() {
        // load Common
        logger.info("___________________开始初始化UploadConfig_________________");
        List<ConfigDo> configs = configService.listConfig(ConfigTypeEnum.UPLOAD.getType());
        load(configs);
        logger.info("___________________初始化UploadConfig完成 Common_________________mqs={}", JSONObject.toJSONString(mqs));

        // load CrawlerPlugin
        List<ClientConfig> clientConfigs = clientConfigService.getConfig();

        if(!CollectionUtils.isEmpty(clientConfigs)) {
            clientConfigs.forEach(clientConfig -> {
                try {
                    String key = ClientConstant.CRAWLER_PLUGIN_PREFIX + clientConfig.getBizLine();

                    if(!mqs.containsKey(key)) {
                        JSONObject mqInfo = JSONObject.parseObject(clientConfig.getConfig()).getJSONObject("target");
                        mqs.put(key, MQInfo.build(mqInfo.getString("group"), mqInfo.getString("topic"), clientConfig.getBizLine()));
                    }
                }
                catch(Exception ex) {
                    logger.error(ex.getMessage(), ex);
                }
            });
        }

        logger.info("___________________初始化UploadConfig完成 CrawlerPlugin_________________mqs={}", JSONObject.toJSONString(mqs));
    }

    @PreDestroy
    private void destroy() {
        mqs = null;
    }
}
