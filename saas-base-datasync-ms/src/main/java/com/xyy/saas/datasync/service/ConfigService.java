package com.xyy.saas.datasync.service;

import com.xyy.saas.datasync.enums.DataSyncTypeEnum;
import com.xyy.saas.datasync.mapper.ConfigMapper;
import com.xyy.saas.datasync.pojo.ConfigDo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ConfigService {
    private static final Logger logger = LoggerFactory.getLogger(ConfigService.class);

    @Resource
    private ConfigMapper configMapper;

    /**
     * 插入
     * @param configDo
     * @return
     */
    public void insert(ConfigDo configDo) {
        // TODO
    }

    /**
     * 查询唯一配置
     * @param bizLine
     * @param configType
     * @param sourceTable 来源表
     * @return
     */
    public ConfigDo getConfig(String bizLine, Integer configType, String sourceTable) {
        if(StringUtils.isEmpty(bizLine) || configType == null || StringUtils.isEmpty(sourceTable)) {
            logger.warn("查询配置参数为空，bizLine={}, configType={}, sourceTable={}", bizLine, configType, sourceTable);
            return null;
        }

        ConfigDo param = new ConfigDo();
        param.setBizLine(bizLine);
        param.setConfigType(configType);
        param.setSourceTable(sourceTable);
        List<ConfigDo> list = configMapper.list(param);

        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        else if(list.size() != NumberUtils.INTEGER_ONE) {
            throw new RuntimeException("发现不唯一配置" + "configType=" + configType + "，sourceTable=" + sourceTable);
        }

        return list.get(NumberUtils.INTEGER_ZERO);
    }

    /**
     * 查询来源表
     * @param bizLine
     * @param configType
     * @param targetTable
     * @return
     */
    public String getSourceTable(String bizLine, Integer configType, String targetTable) {
        List<ConfigDo> list = getConfigByTargetTable(bizLine, configType, targetTable);

        if(CollectionUtils.isEmpty(list)) {
            return null;
        }

        if(list.size() > NumberUtils.INTEGER_ONE) {
            for(ConfigDo item : list) {
                if(DataSyncTypeEnum.SINGLE.getType().equals(item.getDatasyncType()) ||
                        DataSyncTypeEnum.ALL.getType().equals(item.getDatasyncType()))
                {
                    return item.getSourceTable();
                }
                if (targetTable.equals(item.getSourceTable())) {
                    return item.getSourceTable();
                }
            }
        }
        else {
            return list.get(NumberUtils.INTEGER_ZERO).getSourceTable();
        }

        throw new RuntimeException("根据目标表查询查询来源表异常，bizLine=" + bizLine + "，configType=" + configType + "，targetTable=" + targetTable);
    }

    /**
     * 查询数据同步类型
     * @param bizLine
     * @param configType
     * @param targetTable
     * @return
     */
    public boolean isAllDataSyncType(String bizLine, Integer configType, String targetTable) {
        List<ConfigDo> list = getConfigByTargetTable(bizLine, configType, targetTable);

        if(!CollectionUtils.isEmpty(list)) {
            for(ConfigDo configDo : list) {
                if(DataSyncTypeEnum.get(configDo.getDatasyncType()) == DataSyncTypeEnum.ALL) {
                    return true;
                }
            }
        }

        return false;
    }

    private List<ConfigDo> getConfigByTargetTable(String bizLine, Integer configType, String targetTable) {
        if(StringUtils.isEmpty(bizLine) || configType == null || StringUtils.isEmpty(targetTable)) {
            logger.warn("查询配置参数为空，bizLine={}, configType={}, targetTable={}", bizLine, configType, targetTable);
            return null;
        }

        ConfigDo param = new ConfigDo();
        param.setBizLine(bizLine);
        param.setConfigType(configType);
        param.setTargetTable(targetTable);
        return configMapper.list(param);
    }

    /**
     * 查询配置
     * @param configType
     * @return
     */
    public List<ConfigDo> listConfig(Integer configType) {
        if(configType == null) {
            return null;
        }

        ConfigDo param = new ConfigDo();
        param.setConfigType(configType);
        return configMapper.list(param);
    }

    /**
     * 查询配置
     * @param configType
     * @param updateTIme
     * @return
     */
    public List<ConfigDo> listConfig(Integer configType, Date updateTIme) {
        if(configType == null || updateTIme == null) {
            return null;
        }

        ConfigDo param = new ConfigDo();
        param.setConfigType(configType);
        param.setUpdateTime(updateTIme);
        return configMapper.list(param);
    }

    /**
     * 根据tableGroup查询config
     * @param bizLine
     * @param configType
     * @param tableGroup
     * @return
     */
    public List<ConfigDo> queryByGroup(String bizLine, Integer configType, String tableGroup){
        ConfigDo param = new ConfigDo();
        param.setBizLine(bizLine);
        param.setConfigType(configType);
        param.setTableGroup(tableGroup);
        return configMapper.list(param);
    }
}
