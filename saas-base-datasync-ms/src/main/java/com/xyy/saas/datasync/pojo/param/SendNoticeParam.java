package com.xyy.saas.datasync.pojo.param;

import com.xyy.saas.datasync.enums.NoticeTypeEnum;

import java.util.List;

/**
 * 消息通知消息体
 */
public class SendNoticeParam {
    /**
     * code
     */
    private String code;
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 机构
     */
    private String organSign;
    /**
     * 通知类型
     */
    private String noticeType;
    /**
     * 表名
     */
    @Deprecated
    private String tableName;
    /**
     * 表名集合
     */
    private List<String> tableNameList;

    /**
     * 构建对象
     * @param bizLine
     * @param organSign
     * @param tableName
     * @return
     */
    @Deprecated
    public static SendNoticeParam build(String bizLine, String organSign, String tableName) {
        SendNoticeParam param = new SendNoticeParam();
        param.setCode("newDataSync");
        param.setBizLine(bizLine);
        param.setOrganSign(organSign);
        param.setNoticeType(NoticeTypeEnum.DATA_SYNC.getType());
        param.setTableName(tableName);
        return param;
    }

    /**
     * 构建对象
     * @param bizLine
     * @param organSign
     * @param tableNameList
     * @return
     */
    public static SendNoticeParam build(String bizLine, String organSign, String tableName, List<String> tableNameList) {
        SendNoticeParam param = new SendNoticeParam();
        param.setCode("newDataSync");
        param.setBizLine(bizLine);
        param.setOrganSign(organSign);
        param.setNoticeType(NoticeTypeEnum.DATA_SYNC.getType());
        param.setTableName(tableName);
        param.setTableNameList(tableNameList);
        return param;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getTableNameList(){
        return tableNameList;
    }

    public void setTableNameList(List<String> tableNameList){
        this.tableNameList = tableNameList;
    }
}
