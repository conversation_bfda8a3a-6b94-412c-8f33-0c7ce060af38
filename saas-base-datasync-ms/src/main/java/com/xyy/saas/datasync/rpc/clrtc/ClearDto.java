package com.xyy.saas.datasync.rpc.clrtc;

import lombok.Data;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class ClearDto {
    private String bizLine;
    private String organSign;
    private String targetTable;
    /** 当前版本 not null */
    private Long currentVersion;
    /** currentVersion 最早记录时间 */
    private long earliestAt;
    /** 是否已写入数据库 */
    private boolean flushToDb = false;

    public ClearDto(String bizLine, String organSign, String targetTable) {
        this.bizLine = bizLine;
        this.organSign = organSign;
        this.targetTable = targetTable;
    }
}
