package com.xyy.saas.datasync.config;

import com.alibaba.dubbo.config.ReferenceConfig;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.enums.ConfigTypeEnum;
import com.xyy.saas.datasync.pojo.ConfigDo;
import com.xyy.saas.datasync.pojo.dto.DownLoadExtend;
import com.xyy.saas.datasync.service.ConfigService;
import com.xyy.saas.datasync.spi.DataSyncAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import javax.annotation.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 客户端下载数据相关配置
 */
@Component
public class DownloadConfigManager {
    public static final String GROUP = "DOWNLOAD_GROUP";
    public static final String TOPIC = "DOWNLOAD_TOPIC";

    private static final Logger logger = LoggerFactory.getLogger(DownloadConfigManager.class);

    // 接收业务表数据便跟MQ实例
    private Set<MQInfo> mqs = new HashSet<>();
    // 回调业务Dubbo服务集合
    private Map<String, DataSyncAdapter> services = new ConcurrentHashMap<>();

    @Resource
    private ConfigService configService;

    /**
     * 根据table获取业务Dubbo服务
     * @param table
     * @return
     */
    public DataSyncAdapter getDataSyncAdapter(String bizLine, String table) {
        return services.get(bizLine + "-" + table);
    }

    /**
     * 获取数据来源MQ
     * @return
     */
    public Set<MQInfo> getMqs() {
        return mqs;
    }

    /**
     * 根据topic获取业务线
     * @param topic
     * @return
     */
    public String getBizLine(String topic) {
        for(MQInfo mq : mqs) {
            if(Objects.equals(mq.getTopic(), topic)) {
                return mq.getBizLine();
            }
        }

        return null;
    }

    /**
     * 加载配置
     * @param configs
     */
    public void load(List<ConfigDo> configs) {
        if(CollectionUtils.isEmpty(configs)) {
            return;
        }
        logger.warn("加载DownloadConfig信息");
        configs.forEach(config -> {
            try {
                DownLoadExtend downLoadExtend = JSONObject.parseObject(config.getExtend(), DownLoadExtend.class);

                if(downLoadExtend != null) {
                    // 1.解析topic
                    mqs.add(MQInfo.build(downLoadExtend.getSourceGroup(), downLoadExtend.getSourceTopic(), config.getBizLine()));

                    // 2.解析call service
                    String key = config.getBizLine() + "-" + config.getSourceTable();
                    if(!services.containsKey(key)) {
                        services.put(key, initReference(downLoadExtend.getCallServiceVersion(), downLoadExtend.getCallServiceGroup()));
                    }
                }
            }
            catch(Exception ex) {
                logger.error(ex.getMessage() + ", 初始化配置异常，config=" + JSONObject.toJSONString(config), ex);
            }
        });

        logger.info("配置加载完成，configs={}", JSONObject.toJSONString(configs));
    }

    @PostConstruct
    private void init() {
        logger.info("___________________开始初始化DownloadConfig_________________");
        List<ConfigDo> configs = configService.listConfig(ConfigTypeEnum.DOWNLOAD.getType());
        load(configs);
        logger.info("___________________初始化DownloadConfig完成_________________mqs={}, services={}",
                JSONObject.toJSONString(mqs), JSONObject.toJSONString(services));
    }

    @PreDestroy
    private void destroy() {
        mqs = null;
        services = null;
    }

    /**
     * 实例化Dubbo服务
     * @param version
     * @param group
     * @return
     */
    private DataSyncAdapter initReference(String version, String group) {
        ReferenceConfig<DataSyncAdapter> reference = new ReferenceConfig<>();
        reference.setCheck(Boolean.FALSE);
        reference.setVersion(version);
        reference.setInterface(DataSyncAdapter.class);
        reference.setGroup(group);
        return reference.get();
    }
}
