package com.xyy.saas.datasync.mapper;

import com.xyy.saas.datasync.pojo.EventDo;
import com.xyy.saas.datasync.pojo.param.EventDoParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 事件Mapper
 */
@Mapper
public interface EventMapper {

    /**
     * 批量insert
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<EventDo> list);

    /**
     * 获取count
     * @param param
     * @return
     */
    int count(EventDoParam param);

    /**
     * 删除事件
     * @param ids
     * @return
     */
    int delete(List<Long> ids);

    /**
     * 根据条件查询
     * @param param
     * @return
     */
    List<EventDo> list(EventDoParam param);

    /**
     * 根据条件查询ID
     * @param param
     * @return
     */
    List<Long> listIdByCondition(EventDoParam param);

    /**
     * 根据ID查询实体
     * @param idList
     * @return
     */
    List<EventDo> listByIds(@Param("idList") List<Long> idList);

    /**
     * 根据条件查询最大版本事件
     * @param eventDo
     * @return
     */
    EventDo getMaxEvent(EventDo eventDo);

    /**
     * 查询小于createTime的最大ID
     * @param createTime
     * @return
     */
    Long getMaxIdByCreateTime(Date createTime);

    /**
     * 根据ID范围删除数据
     * @param maxId
     * @param pageSize
     * @return
     */
    int deleteEvent(@Param("maxId") Long maxId, @Param("pageSize") Integer pageSize);

    /**
     * 根据ID删除数据: 删除 id < currentId 的事件
     *    <br> 每次限制删除行数: deleteLimit   如果为null, 则不限制删除行数
     * @param bizLine
     * @param organSign
     * @param targetTable
     * @param currentId
     * @param deleteLimit
     * @return
     */
    int deleteOld(@Param("bizLine") String bizLine, @Param("organSign") String organSign,
            @Param("targetTable") String targetTable, @Param("currentId") Long currentId,
            @Param("deleteLimit") Integer deleteLimit);
}
