package com.xyy.saas.datasync.pojo;

import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.api.client.pojo.UploadParam;

public class ClientData {
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 企业ID
     */
    private Long outUniqueId;
    /**
     * 任务ID
     */
    private Integer taskId;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 数据集
     */
    private String data;
    /**
     * 当前偏移量
     */
    private String currentOffset;

    public static ClientData build(UploadParam uploadParam) {
        ClientData clientData = new ClientData();
        clientData.setBizLine(uploadParam.getBizLine());
        clientData.setOutUniqueId(uploadParam.getOutUniqueId());
        clientData.setTaskId(uploadParam.getTaskId());
        clientData.setTableName(uploadParam.getTableName());
        clientData.setData(JSONObject.toJSONString(uploadParam.getData()));
        clientData.setCurrentOffset(uploadParam.getCurrentOffset());
        return clientData;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public Long getOutUniqueId() {
        return outUniqueId;
    }

    public void setOutUniqueId(Long outUniqueId) {
        this.outUniqueId = outUniqueId;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getCurrentOffset() {
        return currentOffset;
    }

    public void setCurrentOffset(String currentOffset) {
        this.currentOffset = currentOffset;
    }
}
