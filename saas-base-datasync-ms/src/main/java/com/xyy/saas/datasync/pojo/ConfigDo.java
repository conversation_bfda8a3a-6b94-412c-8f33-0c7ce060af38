package com.xyy.saas.datasync.pojo;

import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 配置实体
 */
@EqualsAndHashCode
public class ConfigDo {
    /**
     * 配置ID
     */
    private Long id;
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 配置类型
     * com.xyy.saas.datasync.enums.ConfigTypeEnum
     */
    private Integer configType;
    /**
     * 来源表
     */
    private String sourceTable;
    /**
     * 目标表
     */
    private String targetTable;
    /**
     * 数据同步类型
     * com.xyy.saas.datasync.enums.DataSyncTypeEnum
     */
    private Integer datasyncType;
    /**
     * 扩展信息
     *
     * 上传
     *  MQ相关信息
     * 下载
     *  MQ相关信息
     *  dubbo相关信息
     */
    private String extend;
    /**
     * targetTable关联表群组
     */
    private String tableGroup;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public Integer getConfigType() {
        return configType;
    }

    public void setConfigType(Integer configType) {
        this.configType = configType;
    }

    public String getSourceTable() {
        return sourceTable;
    }

    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    public String getTargetTable() {
        return targetTable;
    }

    public void setTargetTable(String targetTable) {
        this.targetTable = targetTable;
    }

    public Integer getDatasyncType() {
        return datasyncType;
    }

    public void setDatasyncType(Integer datasyncType) {
        this.datasyncType = datasyncType;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTableGroup(){
        return tableGroup;
    }

    public void setTableGroup(String tableGroup){
        this.tableGroup = tableGroup;
    }
}
