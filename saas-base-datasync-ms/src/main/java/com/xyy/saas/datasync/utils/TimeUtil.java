package com.xyy.saas.datasync.utils;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class TimeUtil {

    /**
     * 时间单位
     */
    private static final String[][] UNIT_ARR = new String[][]{
            new String[]{"天", "时", "分", "秒", "毫秒"},
            new String[]{"D", "H", "M", "S", "MS"},
            new String[]{"d", "h", "m", "s", "ms"}
    };

    /**
     * 耗时
     * @param cost
     * @param units 单位：天 时 分 秒 毫秒
     * @return
     */
    public static String costToStr(long cost, String... units) {
        if (cost <= 0) {
            return "";
        }
        long days = cost / (1000 * 60 * 60 * 24);
        long left = cost % (1000 * 60 * 60 * 24);
        long hours = left / (1000 * 60 * 60);
        left = left % (1000 * 60 * 60);
        long minutes = left / (1000 * 60);
        left = left % (1000 * 60);
        long seconds = left / 1000;
        left = left % 1000;
        StringBuilder sb = new StringBuilder();
        if (units == null || units.length != UNIT_ARR[0].length) {
            units = UNIT_ARR[2];
        }
        if (days > 0) {
            sb.append(days).append(units[0]);
        }
        if (hours > 0) {
            sb.append(hours).append(units[1]);
        }
        if (minutes > 0) {
            sb.append(minutes).append(units[2]);
        }
        if (seconds > 0) {
            sb.append(seconds).append(units[3]);
        }
        if (left > 0) {
            sb.append(left).append(units[4]);
        }
        return sb.toString();
    }
}
