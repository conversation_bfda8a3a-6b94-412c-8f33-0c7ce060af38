package com.xyy.saas.datasync.rpc;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.datasync.api.DownloadAPi;
import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.api.pojo.request.LoopCheckData;
import com.xyy.saas.datasync.api.pojo.request.*;
import com.xyy.saas.datasync.api.pojo.response.*;
import com.xyy.saas.datasync.enums.ConfigTypeEnum;
import com.xyy.saas.datasync.enums.EventTypeEnum;
import com.xyy.saas.datasync.enums.ExceptionCodeEnum;
import com.xyy.saas.datasync.pojo.EventDo;
import com.xyy.saas.datasync.pojo.param.EventDoParam;
import com.xyy.saas.datasync.pojo.param.IdParam;
import com.xyy.saas.datasync.remote.DataSyncAdapterRemote;
import com.xyy.saas.datasync.rpc.clrtc.ClearManager;
import com.xyy.saas.datasync.service.CanalConsumerService;
import com.xyy.saas.datasync.service.ConfigService;
import com.xyy.saas.datasync.service.EventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Service(version = "0.0.1", timeout = 60000)
@SuppressWarnings("unchecked")
@Slf4j
public class DownloadApiImpl implements DownloadAPi {
    private static final Logger logger = LoggerFactory.getLogger(DownloadApiImpl.class);

    @Resource
    private EventService eventService;
    @Resource
    private ConfigService configService;
    @Resource
    private DataSyncAdapterRemote dataSyncAdapterRemote;
    @Resource
    private CanalConsumerService canalConsumerService;

    @Resource
    private ClearManager clearManager;

    /**
     * 降级开关(canal降级)(null或0:关闭, 1:开启)
     */
    @Value("${datasync.demotion.switch:0}")
    private Integer demotionSwitch;

    /**
     * 数据同步调整分页size为100的表名
     */
    @Value("#{'${datasync.page.size100.tables:medicare_tips_detail}'.split(',')}")
    private Set<String> mixPageSize100Tables;

    /**
     * 增量数据同步调整分页size的表名
     */
    @Value("#{'${increment.datasync.page.size.tables:MEDICARE_REVIEW_RULE_DETAIL}'.split(',')}")
    private Set<String> mixPageSizeIncrementTables;

    /**
     * 增量数据同步分页size
     */
    @Value("${increment.datasync.page.size:100}")
    private Integer incrementSyncPageSize;

    /**
     * 下载增量数据
     *
     * @param param
     * @return
     */
    @Override
    public Result<DownloadResp> pullDataByVersion(DownloadParam param) {
        // 1.数据校验
        if(param == null) {
            return Result.fail(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        // 调整数据分页size
        if (mixPageSizeIncrementTables != null && mixPageSizeIncrementTables.contains(param.getTableName())) {
            param.setPageSize(incrementSyncPageSize);
        }
        param.check();

        // currentVersion传入小于0时,特殊处理
        Long currentVersion = param.getCurrentVersion();
        if (currentVersion != null && currentVersion.compareTo(0L) < 0) {
            log.error("[currentVersion]当前版本号小于0时需要全量同步数据, request:{}", JSON.toJSONString(param));
            return Result.fail(8010, "[currentVersion]当前版本号小于0时需要全量同步数据");
        }

        // 降级开启后的增量数据拉取逻辑
        if (demotionSwitch == 1) {
            DownloadResp result = new DownloadResp();
            BaseDownloadAllResp resp = dataSyncAdapterRemote.listDataByUpdateTime(param);
            List<Object> data = resp.getData();
            Integer dataSize = CollectionUtils.isEmpty(data) ? NumberUtils.INTEGER_ZERO : data.size();
            result.setHasData(param.getPageSize().compareTo(dataSize) <= 0);
            result.setMaxUpdateTime(resp.getMaxUpdateTime() == null ? System.currentTimeMillis() : resp.getMaxUpdateTime().getTime());

            List<DownloadData> items = new ArrayList<>();
            if (!CollectionUtils.isEmpty(data)) {
                data.forEach(object -> {
                    items.add(DownloadData.build(param.getOrganSign(), param.getTableName(), EventTypeEnum.INSERT.getType(), object));
                });
            }
            result.setData(items);
            return Result.success(result);
        }

        // 正常增量拉取数据逻辑

        // 2.事件查询
        EventDoParam condition = EventDoParam.build(param, configService.isAllDataSyncType(param.getBizLine(), ConfigTypeEnum.DOWNLOAD.getType(), param.getTableName()));

        List<Long> idList = eventService.listIdByCondition(condition);
        if (CollectionUtils.isEmpty(idList)) {
            logger.info("未拉取到数据，param={}", JSONObject.toJSONString(param));
            return Result.success();
        }
        List<EventDo> data = eventService.listByIds(idList);

        // 3.查询事件对应数据
        Map<String, Map<Long, Object>> objectMap = getEventData(param.getBizLine(), param.getOrganSign(), data);

        // 4.数据组装
        DownloadResp result = new DownloadResp();
        int size = data.size();
        result.setHasData(size >= condition.getLimit());
        EventDo lastEvent = data.get(size - NumberUtils.INTEGER_ONE);
        result.setMaxVersion(lastEvent.getId());
        long maxUpdateTime = System.currentTimeMillis();
        if (lastEvent.getBizDataUpdateTime() != null) {
            maxUpdateTime = lastEvent.getBizDataUpdateTime().getTime();
        }
        result.setUpdateTime(new Date(maxUpdateTime));
        result.setMaxUpdateTime(maxUpdateTime);
        List<DownloadData> items = new ArrayList<>();

        data.forEach(eventDo -> {
            if(EventTypeEnum.DELETE.getType().equals(eventDo.getEventType())) {
                items.add(DownloadData.build(eventDo.getOrganSign(), eventDo.getTargetTable(), eventDo.getEventType(), IdParam.build(eventDo.getSourceTableId())));
            }
            else {
                Map<Long, Object> stMap = objectMap.get(eventDo.getSourceTable());
                Object object = stMap != null ? stMap.get(eventDo.getSourceTableId()) : null;

                if(object != null) {
                    items.add(DownloadData.build(eventDo.getOrganSign(), eventDo.getTargetTable(), eventDo.getEventType(), object));
                }
                else {
                    /**
                     * INSERT UPDATE 数据为空的不下发到客户端
                     */
                    logger.warn("业务数据为空，不下发到客户端，eventDo={}", JSONObject.toJSONString(eventDo));
                }
            }
        });

        result.setData(items);
        return Result.success(result);
    }

    /**
     * 下载全量数据
     *
     * @param param
     * @return
     */
    @Override
    public Result<DownloadAllResp> pullDataAll(DownloadAllParam param) {
        // 调整数据分页size
        if (mixPageSize100Tables != null && mixPageSize100Tables.contains(param.getTableName())) {
            param.setPageSize(100);
        }

        // 1.参数校验
        if(param == null) {
            return Result.fail(ExceptionCodeEnum.PARAM_IS_NULL.getMsg());
        }

        param.check();
        // 在查询  listData  之前， 这样防止在listData查询后有新增记录被漏掉
        Optional<Long> maxEventOpt = Optional.ofNullable(param.getTableId() == null ? null :
                                                                    eventService.getMaxId(param.getBizLine(), param.getOrganSign(), param.getTableName()));
        BaseDownloadAllResp resp = dataSyncAdapterRemote.listData(param);
        Pagination pagination = resp.getPagination();
        List<Object> data = resp.getData();
        Integer dataSize = CollectionUtils.isEmpty(data) ? NumberUtils.INTEGER_ZERO : data.size();
        DownloadAllResp result = new DownloadAllResp();
        if (pagination != null) {
            result.setHasData(pagination.getTotal() >
                    Pagination.offset(pagination.getCurrentPage(), pagination.getPageSize()) + dataSize);
            result.setCurrentPage(pagination.getCurrentPage());
            result.setPageSize(pagination.getPageSize());
        } else {
            result.setHasData(param.getPageSize().compareTo(dataSize) <= 0);
            result.setCurrentPage(1);
            result.setPageSize(param.getPageSize());
        }
        result.setMaxTableId(resp.getMaxTableId());

        if (param.getTableId() == null || param.getTableId() <= 0 ) {
            long maxVersion = 1L;
            long maxUpdateTime = System.currentTimeMillis();
            EventDo eventDo = eventService.getMaxEvent(param.getBizLine(), param.getOrganSign(), param.getTableName());

            if(eventDo != null) {
                maxVersion = eventDo.getId();
                if (eventDo.getBizDataUpdateTime() != null) {
                    maxUpdateTime = eventDo.getBizDataUpdateTime().getTime();
                }
            }
            result.setMaxVersion(maxVersion);
            result.setUpdateTime(new Date(maxUpdateTime));
            result.setMaxUpdateTime(maxUpdateTime);
        }
        maxEventOpt.ifPresent(result::setMaxVersion);

        List<DownloadData> items = new ArrayList<>();
        if (!CollectionUtils.isEmpty(data)) {
            data.forEach(value -> {
                items.add(DownloadData.build(param.getOrganSign(), param.getTableName(), EventTypeEnum.INSERT.getType(), value));
            });
        }

        result.setData(items);
        return Result.success(result);
    }

    /**
     * 客户端轮训补偿监测
     *
     * @param param
     * @return
     */
    @Override
    public Result<LoopCheckResp> loopCheck(LoopCheckParam param) {
        // 1.参数校验
        LoopCheckParam.check(param);

        List<LoopCheckData> items = param.getData();

        items.forEach(item -> {
            EventDoParam eventDoParam = new EventDoParam();
            eventDoParam.setBizLine(param.getBizLine());
            eventDoParam.setOrganSign(param.getOrganSign());
            eventDoParam.setTargetTable(item.getTableName());
            eventDoParam.setId(item.getCurrentVersion());
            eventDoParam.setLimit(1);
            List<Long> data = eventService.listIdByCondition(eventDoParam);
            item.setHasData(!CollectionUtils.isEmpty(data));
            // 查询Apollo中配置,看是否新表
            item.setNewTable(canalConsumerService.isNewTable(param.getOrganSign(), item.getTableName()));

            if (clearManager.getClearSwitch()) {
                clearManager.refreshCache(param.getBizLine(), param.getOrganSign(), item.getTableName(), item.getCurrentVersion());
            }
        });

        return Result.success(LoopCheckResp.build(param.getBizLine(), param.getOrganSign(), items));
    }

    /**
     * 获取事件ID对应的业务数据
     * @return
     */
    private Map<String, Map<Long, Object>> getEventData(String bizLine, String organSign, List<EventDo> data) {
        Map<String, List<Long>> sourceTableToIds = new HashMap<>();

        data.forEach(item -> {
            List<Long> ids = sourceTableToIds.computeIfAbsent(item.getSourceTable(), k -> new ArrayList<>());
            ids.add(item.getSourceTableId());
        });

        Map<String, Map<Long, Object>> result = new HashMap<>();

        sourceTableToIds.forEach((key, val) -> {
            result.put(key, dataSyncAdapterRemote.listDataByIds(bizLine, organSign, key, val));
        });

        return result;
    }
}
