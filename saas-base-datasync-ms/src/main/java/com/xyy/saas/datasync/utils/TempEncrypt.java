package com.xyy.saas.datasync.utils;

import com.alibaba.druid.filter.config.ConfigTools;

public class TempEncrypt {
    private static void dev() throws Exception {
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAorpS8yCCki9f4kot+6fG3ZgE6AUOe2q4pI84pmk9YocsfJhKhbI1ZvdFsf/hjzoQI9b8osJVhWptEEiJtkI7Nin1JdWaXjd3Bu4hzS4RSI+2zpvA798r9mHHLIdaPvU5HKXiSigN6DBYSaeL9sFntNQNYR8uXixPx44LFdQoIXbgwYl/Au34Ay/vJuOasp94EkWSinJLkVmcAcMsrrHh0IgcUXZvSBg9wBpizt8pNrw0wx1YCDG1EFP0NoP+YXzMS/vnnLAUDuTe5Ke3vunl1cT54VtU9M99fvgq9QF1tpRtwrpUVAiV3SHv702uko8LtB2bPcEVVLY0AkW0x68qFwIDAQAB";
        String privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCiulLzIIKSL1/iSi37p8bdmAToBQ57arikjzimaT1ihyx8mEqFsjVm90Wx/+GPOhAj1vyiwlWFam0QSIm2Qjs2KfUl1ZpeN3cG7iHNLhFIj7bOm8Dv3yv2Yccsh1o+9TkcpeJKKA3oMFhJp4v2wWe01A1hHy5eLE/HjgsV1CghduDBiX8C7fgDL+8m45qyn3gSRZKKckuRWZwBwyyuseHQiBxRdm9IGD3AGmLO3yk2vDTDHVgIMbUQU/Q2g/5hfMxL++ecsBQO5N7kp7e+6eXVxPnhW1T0z31++Cr1AXW2lG3CulRUCJXdIe/vTa6Sjwu0HZs9wRVUtjQCRbTHryoXAgMBAAECgf8MtoHEfMW2pfzXsPPmtTTUGwxHFB4Nhkjmkknatl4VVHolQcCQsVt6r9bCETuN9PtN0X3rOUN1KNtcak5X7tSxZpXAXu+wbKTf5bq+TNsM0C139rMcClNk7D6jO+/Q+1d65XrIUFjFywEav/wvSuzPM8HX1JfKw6t366DyE6EO/BqC/fPW6oDUv3ZCxmFcA1FAJgE+Zwpps8ZndWIBxqEFoJx0E6Ub/6sRucIFLwkzUo/O45MC8Drb32XzToaXxbsXgYPyghcMUP/cccs5BzEtUvW0nc/AHrvzDHuw75Mc6pBBdVXtsj0zaTgKiMBRGK4DyeZy/ccffApRVFH87MECgYEA4mO0LdQOrwwVbypIaxO4KzP+swu+eUk3obh75WB9avWlz4rVykfhL8wcLmUNZ77yqv266WefjSzORvdDhhFDPoArzemaMNa/o9YJiFmcP8VbBhkVN6B5B31FeUUuT0CLIYQGOnu6DAEgIuAHQPvtDsX0JC9iipPVVAhyxv6SUMkCgYEAuAMCk1b0f2cWv9/5YRneoxhQZ0ZJx9g5LB/jmvKSuXEzy73SldGTgEiNN6qRCVmZYbLQvLOb3a8CYrY6X/+bz4jIU+X7z0yLBZW8M6eVI3A1ADQZGKIDdR4Tjp2pWQlaPT5siBfl/mB8bawJFeViCE6Q9AL1PH2FL06xKbks898CgYEAmRn2gzuzBny0ji5ZNVjrpwQNzsY1dU6nuc3KtDASAWDJVtc8iAUebDWsJVAfxbaj5DGd0zVJ+K6BfgrAdo18HWuOtD20Ei1ZAxK/9Z8shRAR48jYyXrT/tWgQ0ge7mfm766ai7ia9H1aX1uEbFUzV1dWKZaGHNOnZKImBR+qkckCgYBq9WTz2+F2hKeWlEvSg0UELEfgH90XCroAyfB0ync6z3A6B3Z2m0FPmdxr9PslIi2julrWtNfuvaF3W1EHJ/6bXXiEO/RohKdycZzZsZlg5gIIwj+sL9AurXLUvRQIqwUHAFf5q1qGx/LLlOobDiAzj+d1zppZtORLoUQdaoOUpQKBgQCylUaw7cuSttT1/IYo+QV4Ir/00VjIaCCRrxnIy0IwdFqmOFpSyv/6CkQKZeA/nrKRNY1lHeF/+M2VXClWAHGlkP8Bc5ofAkgJMbFSrJXXhGXF/IFRjfvOM35Uf4hx8dokxJZQLBBU8xmifejCCBGHLbZd2273y59jEcT4h0HdNw==";
        String password = "udLT11tqYodH7Mf5";
        String encrypt = ConfigTools.encrypt(privateKey, password);
        System.out.println("dev======");
        System.out.println(encrypt);
    }

    private static void test() throws Exception {
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8S3IU5R/pFWeSXd6pbvT+ltPURGjnN0P6RI+KdWHwKo8ngT9e9PfMXa1JvIPjtCag0QmYRJjIkTTc5cXgqbrM8aXlFNJm8FFnS4Er8riHo34hEGJWPqiAq5suF0cPlgG9UgVNLl0NC5kRVtkJgRDNXSt+/d7b0dmhfNll9DNI2bZY9RguToB7K2V+2utkjrT27/JoeFeMsK2YYUJjaxaCaT9/ZxVUgtfHz0m7z0AkAUCjI00RB/2ud3kY5MEiA8xWXdSMDrCRibAKgj2cDv9lqRQOd/a0BUgusFYdDi7QYsyo2bG3/ZvpIi0OxqRMT9Pyq8V8eVBMwsm1aywF7tFtQIDAQAB";
        String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDxLchTlH+kVZ5Jd3qlu9P6W09REaOc3Q/pEj4p1YfAqjyeBP17098xdrUm8g+O0JqDRCZhEmMiRNNzlxeCpuszxpeUU0mbwUWdLgSvyuIejfiEQYlY+qICrmy4XRw+WAb1SBU0uXQ0LmRFW2QmBEM1dK3793tvR2aF82WX0M0jZtlj1GC5OgHsrZX7a62SOtPbv8mh4V4ywrZhhQmNrFoJpP39nFVSC18fPSbvPQCQBQKMjTREH/a53eRjkwSIDzFZd1IwOsJGJsAqCPZwO/2WpFA539rQFSC6wVh0OLtBizKjZsbf9m+kiLQ7GpExP0/KrxXx5UEzCybVrLAXu0W1AgMBAAECggEAZrPcTts5z701/osGi3UEu+QOTrN4POBWWiYOmrn3aTAVSCVP3F9I+ENYuRDzVxm18pgez8+Ir5sngF2jAVwbZJz5B4zAF7eh8WEbVdDLob9LSOavmliAjSvB2HCuCk5qCCF+BknDFkV7JZ3Zc+b0x9q8DERDKAv7uKcBaDjeYmgiH000rcrQEUTLLwizphhgu6fMpnRJcWRyPmiddti7EZk7Q6xRVSVdzbQBIhUNmkrUAFOv+UpNfRKZ8A3HHL/2F3HYMT9BIVTAHJnJcr6DG7tQ2fSfcAibbNv6AETRxq6R0sH2xpuncrCUkQiFInetw2VxaUAWBf0w4w6YTrHfgQKBgQD7O/I+7F+12VyOOw9E8msTbvA+VfcgafTpY2wARVHimE73MJDM97Ir7/4hPiYCaHtlehXGhum7vlP0htIyVwrBmEabNXgAoP32mCPrJmcetHyw+5ZPKdinWTIt/XPcJ+7MKshB2eyUdS5iIra8y7IGQAaqIeEvnnxXWUX3BtZqbQKBgQD1wQFSBc2odxL638U2jP0A7eNkRbSZPfsU/iUb7VZNhOPesDV/wOobMk/NF/Q/lQ8mv4ukqHXBZBwzRyA6VjtD1vxlM5CmahFmvvjD/bIuAehlenw9taV92BRxDpx91YtME9QKBtwxR5G7fx4YUqXnzXs1jp32JwC2dbD+Sry7aQKBgFPC5r8n27krYPpionKgSCbQg/SkgKIj8W2moils8yITGbC8Vmqf4p1XhGjc6Zs/7nQkwgHQlTjs+dy63nW+I23sMQ4qQ3oiMsn2a3NI+HR6RGBVYQW/s+YHjP1cVTZ/cdRMJzwdRg0xDGfXnwlO+GmxCwfKB+PyeQ4+S19SvkX5AoGBAPCCBNLLDyxR7GdNW+o5LNod9i6J8WcPu4wd9R9RtOaEqaWkeZcfq30WTfb//G/FZ2CakQWCQEwVt2uPrkCpZ7LK0ZQRVP0oTQNq3nag8CIKh+/YYYLbJKz2LXs/u3OC66J2vUtaz2ADFOKUU1WmfOtQs8YEXx8pqI7ou2ECr5YRAoGAChyc5xxpn12wveAldASlC0lJrvjibepL0eQvtTIrNkBV/8bFvwMdmp20pCdm6pTcOMaienLeX/OmT2UJBoekrboCEOzjYUGfJm8tN1ZPkSlBdstFlkmjsMXlUWw4ekp4MTgJC2Ao5Q0/Og8wjNisMoYTyWM6T9gDPXBXblfrij0=";
        String password = "Nsvp7q8tvVQepyXZ";
        String encrypt = ConfigTools.encrypt(privateKey, password);
        System.out.println("test======");
        System.out.println(encrypt);
    }

    private static void prod() throws Exception {
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA52bCkzCmtIB3tNutBeisRwM9kYmJIoW8We9KjevDK/3zNO5eaat6zfHhxaUyvXDoD4G221sh9ZtXheGf0KAt25OPil9JtVyCvpEvS1MiNAhggaLqxAIJKsR8yc9Ci8o11SqpEacRgEQRmxopv8FpjBBcMQbz8BPiQ1iUhNtwYjFpSLgzEWmyY0evMzKMqz/vveoo1wWms766cnBTg/FOpNp1r+K5eYO6IIFIiKkDskAX3VvdoEKeYt2e9YsxRPaTR/kwHJzCNmDoBWSw2Zuf4TkS7CgkG1A/KJeEEKNUTQYg/feQlnhD4a7VdnajvtVGSo19qpzyg9wyjw4nTKM3NwIDAQAB";
        String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnZsKTMKa0gHe0260F6KxHAz2RiYkihbxZ70qN68Mr/fM07l5pq3rN8eHFpTK9cOgPgbbbWyH1m1eF4Z/QoC3bk4+KX0m1XIK+kS9LUyI0CGCBourEAgkqxHzJz0KLyjXVKqkRpxGARBGbGim/wWmMEFwxBvPwE+JDWJSE23BiMWlIuDMRabJjR68zMoyrP++96ijXBaazvrpycFOD8U6k2nWv4rl5g7oggUiIqQOyQBfdW92gQp5i3Z71izFE9pNH+TAcnMI2YOgFZLDZm5/hORLsKCQbUD8ol4QQo1RNBiD995CWeEPhrtV2dqO+1UZKjX2qnPKD3DKPDidMozc3AgMBAAECggEBAM0rs8HGFUldzsWxMUxX7MhZ+zEbfVf6s/IZ1UTYkYhj9kwsPdH7lTsdCE2yF7cYcOmu3W7I8udOAOBYGNdl7sMNpuvNaRFBITvOhtyGrYDtHIRU60s3cuFUrkBRYHOJqwmCdb2NIFtV/zZCkFnRJ8BkkRl3TLrFJo0H8UeLOa4U6aD88CjFJeaIw8F87N7icaqmxlGmE7Ug1Z+39Hm4gKtDZZa8UQakNx3tUnstKwHifw0Sed0JqC+NyNQISIVBhiD0sFEKRFcg04EiEYezuTedmTaNeQm9bGk8jl88kOM541QYDsVIdHKL6hAdU7qGDIkEcVAFG2D2GHVjIoT3W0ECgYEA/BuBJ24g6gaVhO3n2uWyc7gfCbRFNPrbl5KI0dMkt83cVKMP1KOvUEEr6omLxEkRcBvV/7GEEsAwIsojwDtftomGcos0uihsSnhIXbeUgUNbyIEQOAJnKsfWIewfen52R+c/+NLnmDzACtR8fMLL7xbNlogFDM/DBp5jwbwIX+kCgYEA6vlpXq85/Fl1lkSnhMPYqLVGYsA5L8hpAEwQOyQUkObwzYdBbCBv9VgLcIh25pAoAe50BSl/1vc4mCDTZMh7uf/VFdStWguSRWVCcqJHHrUqmKkzTyfy+VDflv41bkay7EDNU4GyN0DEBPZtGoay8T5IklWU8HqyIKD6pj0Mih8CgYAyzAPxAvKk1ODJoPoHlCKpyF6QoP8U1rrqRn9E7+tFEbikYRqueLC5VfhYUJCV4zcdRK6Qz57R/GF2kQ41DIGTkRBDAiOEouJ2+tdx8qx8gyr6Mm8nKezJpOxTd3U8REDm6gRsyWoatZroU7ollUZsDUctXojN9QrRyZZGEfIKCQKBgGfL4SX4Va8JfY3xqeZV9SEhYoRA0YIwD2IYN5tIZuVTfs/rjZTMB2jjiboeLhfPWjzwLsQQ/u3gq1lYNslwXmddga1xE1qRff21q1KkKLKB6N0i8Q1oYkaL2RkEFmouRN9kCM94nVcW/wAKM0vicK1si/ZIY9CPApyzP8WrdhyVAoGAESLf14I/EuPmksHHmY3j9EGLB99uVRim9ApFJJHhQXd5sA+1lNa8emwiZm5YljN2DrUa47QFlVsdyv79cDhk1mXL+Di6brNVnFmf7iMFws9GQE7XHhCh3tSZUmpvjGwVbxji77S8WOt52KmW1oB2BW2p36X3C4XwMln5VnEA3Ko=";
        String password = "fSqbzVmKxy6nBO3K";
        String encrypt = ConfigTools.encrypt(privateKey, password);
        System.out.println("prod======");
        System.out.println(encrypt);
    }

    public static void main(String[] args) {
        try {
            dev();
            test();
            prod();
        }
        catch(Exception ex) {
            ex.printStackTrace();
        }
    }
}
