package com.xyy.saas.datasync;

import com.alibaba.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableDubbo
@EnableTransactionManagement
@ComponentScan(basePackages={"com.xyy.*"})
public class Application {
    public static void main(String[] args) {
        // System.setProperty("cat.appName","saas-base-datasync");
        SpringApplication app=new SpringApplication(Application.class);
        app.run(args);
    }
}
