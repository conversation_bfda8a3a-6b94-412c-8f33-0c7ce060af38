server:
  port: 8210

logging:
  config: classpath:logback-spring.xml
  file: saas-base-datasync-ms

mybatis:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    log-prefix: dao.
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

cat:
  appName: saas-base-datasync-ms

#应用唯一标识，在Apollo Portal（管理界面）创建项目时，同应用id
app:
  id: saas-base-datasync-ms
apollo:
  bootstrap:
    #是否在项目启动的阶段注入默认application namespace的配置
    enabled: true
    eagerLoad:
      # 将apollo初始化放在日志系统初始化之前,保证正确的加载日志配置
      enabled: true