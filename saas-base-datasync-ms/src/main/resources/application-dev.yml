spring:
  datasource:
    url: **************************************************************************************************************************************************************************************************************************************************************************
    username: app_datasync_w
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    druid:
      #当filters添加 stat 表示开启控制台
      filters: config
      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
      #最大连接池数量
      max-active: 50
      #初始化时建立物理连接的个数
      initial-size: 5
      #获取连接时最大等待时间，单位毫秒
      max-wait: 18000
      #最小连接池数量
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      #连接保持空闲而不被驱逐的最小时间
      min-evictable-idle-time-millis: 30000
      validation-query: select 1
      #建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。 缺省：false
      test-while-idle: true
      #申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。 缺省：true
      test-on-borrow: false
      #归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。 缺省：false
      test-on-return: false
      #连接池中的minIdle数量以内的连接，空闲时间超过minEvictableIdleTimeMillis，则会执行keepAlive操作。
      keep-alive: true
dubbo:
  application:
    #当前服务名称
    name: saas-base-datasync-ms
    #telnet端口号
    qos-port: 23200
    #当前应用的版本
    version: 1.0.0
  protocol:
    #协议BeanId
    id: dubbo
    #协议名称
    name: dubbo
    #服务端口
    port: 20201
    #请求及相应数据包大小限制字节8M
    payload: 8388608
    threadpool: fixed
    threads: 500
  registry:
    #注册中心地址协议
    protocol: zookeeper
    #注册中心服务器地址，同一集群内的多个地址用逗号分隔
    address: zk1-dev.zookeeper.ybm100.top:2181,zk2-dev.zookeeper.ybm100.top:2181,zk3-dev.zookeeper.ybm100.top:2181
    #注册中心不存在时，是否报错
    check: false
    #注册中心请求超时时间(毫秒)
    timeout: 6000
  consumer:
    #启动时检查提供者是否存在，true报错，false忽略
    check: false
    #远程服务调用超时时间(毫秒)
    timeout: 6000
  provider:
    #远程服务调用超时时间(毫秒)
    timeout: 6000
    #远程服务调用重试次数，不包括第一次调用，不需要重试请设为0
    retries: -1
    #请求及响应数据包大小限制，单位：字节
    payload: 8388608
management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        defaults: memory
        extras: load,threadpool
apollo:
  meta: http://node01-dev.appolo.ybm100.top:8080,http://node02-dev.appolo.ybm100.top:8080,http://node03-dev.appolo.ybm100.top:8080

rocketMQ:
  mqServers: mq1-dev.rocketmq.ybm100.top:9876;mq2-dev.rocketmq.ybm100.top:9876;mq3-dev.rocketmq.ybm100.top:9876;mq4-dev.rocketmq.ybm100.top:9876
  batchSize: 30