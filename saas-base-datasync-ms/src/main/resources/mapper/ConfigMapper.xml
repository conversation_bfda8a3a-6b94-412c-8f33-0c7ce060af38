<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.datasync.mapper.ConfigMapper">
  <resultMap id="BaseResultMap" type="com.xyy.saas.datasync.pojo.ConfigDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_line" jdbcType="VARCHAR" property="bizLine" />
    <result column="config_type" jdbcType="INTEGER" property="configType" />
    <result column="source_table" jdbcType="VARCHAR" property="sourceTable" />
    <result column="target_table" jdbcType="VARCHAR" property="targetTable" />
    <result column="datasync_type" jdbcType="INTEGER" property="datasyncType" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="table_group" jdbcType="VARCHAR" property="tableGroup" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, biz_line, config_type, source_table, target_table, datasync_type, extend, update_time, create_time, table_group
  </sql>

  <sql id="Condition">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="bizLine != null">
        AND biz_line = #{bizLine}
      </if>
      <if test="configType != null">
        AND config_type = #{configType}
      </if>
      <if test="sourceTable != null">
        AND source_table = #{sourceTable}
      </if>
      <if test="targetTable != null">
        AND target_table = #{targetTable}
      </if>
      <if test="updateTime != null">
        AND update_time > #{updateTime}
      </if>
      <if test="tableGroup != null">
        AND table_group > #{tableGroup}
      </if>
    </where>
  </sql>

  <insert id="insert" parameterType="com.xyy.saas.datasync.pojo.ConfigDo">
      INSERT INTO config (
        biz_line, config_type, source_table, target_table, datasync_type, extend, table_group
      ) VALUES (
        #{bizLine}, #{configType}, #{sourceTable}, #{targetTable}, #{datasyncType}, #{extend}, #{tableGroup}
      )
   </insert>

  <select id="list" parameterType="com.xyy.saas.datasync.pojo.ConfigDo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM config
    <include refid="Condition"/>
    ORDER BY update_time DESC
  </select>
</mapper>