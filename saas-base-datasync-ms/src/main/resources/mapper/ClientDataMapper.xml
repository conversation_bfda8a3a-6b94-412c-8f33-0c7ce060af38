<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.datasync.mapper.ClientDataMapper">
  <insert id="insert" parameterType="com.xyy.saas.datasync.pojo.ClientData">
      INSERT INTO client_data (
        biz_line, out_unique_id, task_id, `table_name`, `data`, current_offset
      ) VALUES (
        #{bizLine}, #{outUniqueId}, #{taskId}, #{tableName}, #{data}, #{currentOffset}
      )
   </insert>
</mapper>