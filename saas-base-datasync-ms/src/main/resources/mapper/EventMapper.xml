<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.datasync.mapper.EventMapper">
  <resultMap id="BaseResultMap" type="com.xyy.saas.datasync.pojo.EventDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_line" jdbcType="VARCHAR" property="bizLine" />
    <result column="source_organ_sign" jdbcType="VARCHAR" property="sourceOrganSign" />
    <result column="organ_sign" jdbcType="VARCHAR" property="organSign" />
    <result column="source_table" jdbcType="VARCHAR" property="sourceTable" />
    <result column="target_table" jdbcType="VARCHAR" property="targetTable" />
    <result column="source_table_id" jdbcType="BIGINT" property="sourceTableId" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="biz_data_update_time" jdbcType="TIMESTAMP" property="bizDataUpdateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, biz_line, source_organ_sign, organ_sign, source_table, target_table, source_table_id, event_type, biz_data_update_time, create_time
  </sql>

  <sql id="Condition">
    <where>
      <if test="id != null">
        id > #{id}
      </if>
      <if test="bizLine != null">
        AND biz_line = #{bizLine}
      </if>
      <if test="organSign != null">
        AND organ_sign = #{organSign}
      </if>
      <if test="sourceTable != null">
        AND source_table = #{sourceTable}
      </if>
      <if test="targetTable != null">
        AND target_table = #{targetTable}
      </if>
    </where>
  </sql>

  <sql id="Pagination">
    <if test="offset != null and limit != null">
      limit #{offset}, #{limit}
    </if>
    <if test="offset == null and limit != null">
      limit #{limit}
    </if>
  </sql>

  <insert id="batchInsert">
    INSERT INTO event (biz_line, source_organ_sign, organ_sign, source_table, target_table, source_table_id, event_type, biz_data_update_time)
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.bizLine}, #{item.sourceOrganSign}, #{item.organSign}, #{item.sourceTable}, #{item.targetTable}, #{item.sourceTableId}, #{item.eventType}, #{item.bizDataUpdateTime})
    </foreach>
   </insert>

  <delete id="delete" parameterType="java.util.List">
       DELETE FROM event WHERE id IN
       <foreach item="id" collection="list" separator="," open="(" close=")">
        #{id}
       </foreach>
  </delete>

  <select id="count" parameterType="com.xyy.saas.datasync.pojo.EventDo" resultType="java.lang.Integer">
    SELECT COUNT(*)
    FROM event
    <include refid="Condition"/>
  </select>

  <select id="list" parameterType="com.xyy.saas.datasync.pojo.param.EventDoParam" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM event force index(idx_bizline_organ_table)
    <include refid="Condition"/>
    ORDER BY id ASC
    <include refid="Pagination"/>
  </select>

  <select id="listIdByCondition" parameterType="com.xyy.saas.datasync.pojo.param.EventDoParam" resultType="long">
    SELECT
    id
    FROM event
    <include refid="Condition"/>
    ORDER BY id ASC
    <include refid="Pagination"/>
  </select>

    <select id="listByIds" parameterType="com.xyy.saas.datasync.pojo.param.EventDoParam" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM event
        where
            id in
            <foreach collection="idList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        ORDER BY id ASC
    </select>

  <select id="getMaxEvent" parameterType="com.xyy.saas.datasync.pojo.EventDo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM event
    <include refid="Condition"/>
    ORDER BY id DESC LIMIT 1
  </select>

    <select id="getMaxIdByCreateTime" parameterType="date" resultType="long">
        select max(id) from event where create_time &lt; #{createTime}
    </select>
    <delete id="deleteEvent" parameterType="map">
        delete from event
        where
        id &lt;= #{maxId}
        order by id asc
        limit #{pageSize}
    </delete>

    <delete id="deleteOld" parameterType="map">
        delete from event
        where biz_line = #{bizLine} AND organ_sign = #{organSign} AND target_table = #{targetTable}
            AND id &lt; #{currentId}
        <if test="deleteLimit != null"> limit #{deleteLimit} </if>
    </delete>
</mapper>