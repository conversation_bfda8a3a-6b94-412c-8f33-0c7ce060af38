<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.datasync.mapper.ClientConfigMapper">
  <resultMap id="BaseResultMap" type="com.xyy.saas.datasync.api.client.pojo.ClientConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_line" jdbcType="VARCHAR" property="bizLine" />
    <result column="out_unique_id" jdbcType="BIGINT" property="outUniqueId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="config" jdbcType="VARCHAR" property="config" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, biz_line, out_unique_id, remark, config, update_time, create_time
  </sql>

  <sql id="Condition">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="bizLine != null">
        AND biz_line = #{bizLine}
      </if>
      <if test="outUniqueId != null">
        AND out_unique_id = #{outUniqueId}
      </if>
    </where>
  </sql>

  <select id="list" parameterType="com.xyy.saas.datasync.api.client.pojo.ClientConfig" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM client_config
    <include refid="Condition"/>
  </select>

    <insert id="upsert" parameterType="com.xyy.saas.datasync.api.client.pojo.ClientConfig">
        insert into client_config
        (
         id,
         biz_line,
         out_unique_id,
         <if test="remark != null">
            remark,
         </if>
         config
         )
        values
        (
         #{id},
         #{bizLine},
         #{outUniqueId},
         <if test="remark != null">
            #{remark},
         </if>
         #{config}
         )
        on duplicate key update
        config = values(config)
    </insert>
</mapper>