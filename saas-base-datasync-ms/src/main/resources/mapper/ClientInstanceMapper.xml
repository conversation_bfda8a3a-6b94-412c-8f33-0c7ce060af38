<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.datasync.mapper.ClientInstanceMapper">
  <resultMap id="BaseResultMap" type="com.xyy.saas.datasync.api.client.pojo.ClientInstance">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_line" jdbcType="VARCHAR" property="bizLine" />
    <result column="out_unique_id" jdbcType="BIGINT" property="outUniqueId" />
    <result column="client_type" jdbcType="INTEGER" property="clientType" />
    <result column="client_config_id" jdbcType="BIGINT" property="clientConfigId" />
    <result column="client_id" jdbcType="BIGINT" property="clientId" />
    <result column="client_key" jdbcType="VARCHAR" property="clientKey" />
    <result column="instance_config" jdbcType="VARCHAR" property="instanceConfig" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, biz_line, out_unique_id, client_type, client_config_id, client_id, client_key, instance_config, update_time, create_time
  </sql>

  <sql id="Condition">
    <where>
      <if test="bizLine != null">
        biz_line = #{bizLine}
      </if>
      <if test="outUniqueId != null">
        AND out_unique_id = #{outUniqueId}
      </if>
      <if test="clientId != null">
        AND client_id = #{clientId}
      </if>
      <if test="clientKey != null">
        AND client_key = #{clientKey}
      </if>
      <if test="updateTime != null">
        AND update_time >= #{updateTime}
      </if>
    </where>
  </sql>

  <sql id="Update">
    <set>
      <if test="instanceConfig != null">
        instance_config = #{instanceConfig}
      </if>
    </set>
  </sql>

  <select id="list" parameterType="com.xyy.saas.datasync.api.client.pojo.ClientInstance" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM client_instance
    <include refid="Condition"/>
  </select>

  <update id="update" parameterType="com.xyy.saas.datasync.api.client.pojo.ClientInstance">
    UPDATE client_instance
    <include refid="Update"/>
    WHERE id = #{id}
  </update>

    <insert id="insert" parameterType="com.xyy.saas.datasync.api.client.pojo.ClientInstance">
        insert into client_instance set
            biz_line = #{bizLine},
            out_unique_id = #{outUniqueId},
            client_type = #{clientType},
            client_config_id = #{clientConfigId},
            client_id = #{clientId},
            client_key = #{clientKey},
            instance_config = #{instanceConfig}
    </insert>
</mapper>