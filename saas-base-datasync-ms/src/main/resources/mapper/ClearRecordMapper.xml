<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.datasync.mapper.ClearRecordMapper">
  <resultMap id="ClearRecordDo" type="com.xyy.saas.datasync.rpc.clrtc.ClearRecordDo">
    <result column="biz_line" jdbcType="VARCHAR" property="bizLine"/>
    <result column="organ_sign" jdbcType="VARCHAR" property="organSign"/>
    <result column="target_table" jdbcType="VARCHAR" property="targetTable"/>
    <result column="last_version" jdbcType="BIGINT" property="lastVersion"/>
    <result column="current_version" jdbcType="BIGINT" property="currentVersion"/>
    <result column="last_delete" jdbcType="BIGINT" property="lastDelete"/>
    <result column="sum_delete" jdbcType="BIGINT" property="sumDelete"/>
    <result column="cnt" jdbcType="BIGINT" property="cnt"/>
    <result column="max_id" jdbcType="BIGINT" property="maxId"/>
    <result column="need_clear" jdbcType="INTEGER" property="needClear"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <resultMap id="ClearStatDo" type="com.xyy.saas.datasync.rpc.clrtc.ClearStatDo">
    <result column="biz_line" jdbcType="VARCHAR" property="bizLine"/>
    <result column="organ_sign" jdbcType="VARCHAR" property="organSign"/>
    <result column="target_table" jdbcType="VARCHAR" property="targetTable"/>
    <result column="cnt" jdbcType="BIGINT" property="cnt"/>
    <result column="max_id" jdbcType="BIGINT" property="maxId"/>
  </resultMap>

  <sql id="Base_Column_List">
    biz_line, organ_sign, target_table, last_version, current_version, last_delete, sum_delete, cnt, max_id, need_clear,
    create_time, update_time
  </sql>

  <!-- 不要自增主键 -->
  <insert id="insertOrUpdate" parameterType="com.xyy.saas.datasync.rpc.clrtc.ClearRecordDo">
    INSERT INTO clear_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bizLine != null" >
        biz_line,
      </if>
      <if test="organSign != null" >
        organ_sign,
      </if>
      <if test="targetTable != null" >
        target_table,
      </if>
      <if test="lastVersion != null" >
        last_version,
      </if>
      <if test="currentVersion != null" >
        current_version,
      </if>
      <if test="lastDelete != null" >
        last_delete,
      </if>
      <if test="sumDelete != null" >
        sum_delete,
      </if>
      <if test="cnt != null" >
        cnt,
      </if>
      <if test="maxId != null" >
        max_id,
      </if>
      <if test="needClear != null" >
        need_clear,
      </if>
    </trim>
    <trim prefix=" VALUES (" suffix=")" suffixOverrides="," >
      <if test="bizLine != null" >
        #{bizLine,jdbcType=VARCHAR},
      </if>
      <if test="organSign != null" >
        #{organSign,jdbcType=VARCHAR},
      </if>
      <if test="targetTable != null" >
        #{targetTable,jdbcType=VARCHAR},
      </if>
      <if test="lastVersion != null" >
        #{lastVersion,jdbcType=BIGINT},
      </if>
      <if test="currentVersion != null" >
        #{currentVersion,jdbcType=BIGINT},
      </if>
      <if test="lastDelete != null" >
        #{lastDelete,jdbcType=BIGINT},
      </if>
      <if test="sumDelete != null" >
        #{sumDelete,jdbcType=BIGINT},
      </if>
      <if test="cnt != null" >
        #{cnt,jdbcType=BIGINT},
      </if>
      <if test="maxId != null" >
        #{maxId,jdbcType=BIGINT},
      </if>
      <if test="needClear != null" >
        #{needClear,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix=" ON DUPLICATE KEY UPDATE " suffixOverrides="," >
      <if test="lastVersion != null">
        last_version = GREATEST(last_version, #{lastVersion}),
      </if>
      <if test="currentVersion != null">
        current_version = GREATEST(current_version, #{currentVersion}),
      </if>
      <if test="lastDelete != null">
        last_delete = #{lastDelete}, sum_delete = sum_delete + #{lastDelete},
      </if>
      <if test="cnt != null">
        cnt = #{cnt},
      </if>
      <if test="maxId != null">
        max_id = #{maxId},
      </if>
      <if test="needClear != null">
        need_clear = #{needClear},
      </if>
    </trim>


  </insert>

  <select id="stat" resultMap="ClearStatDo">
    select biz_line, organ_sign, target_table, count(*) cnt, max(id) max_id from event
    group by biz_line, organ_sign, target_table having count(*) > #{overLimit}
  </select>

  <select id="listForClear" resultMap="ClearRecordDo">
    select
    <include refid="Base_Column_List"/>
    FROM clear_record
    where need_clear = 1 and current_version > last_version
    order by biz_line, organ_sign, target_table
  </select>
</mapper>