<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostLogMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->


  <select id="countInquiryByTenantCostId" resultType="java.lang.Long">
    select sum(case
    when record_type in (4, 6, 7) then ABS(change_cost)
    when record_type in (5, 8) then -ABS(change_cost)
    else 0 end)
    from saas_tenant_package_cost_log
    where tenant_id = #{tenantId}
    and biz_type = #{bizType}
    and cost_id in
    <foreach collection="costIds" open="(" item="id" separator="," close=")">
      #{id}
    </foreach>
  </select>
</mapper>