package com.xyy.saas.inquiry.signature.sign;

import com.xyy.saas.inquiry.signature.server.service.pdf.PdfBoxUtils;
import com.xyy.saas.inquiry.util.UrlConUtil;
import java.io.InputStream;
import java.util.List;
import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

/**
 * 快速PDF转换测试 用于验证修复后的性能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-16
 */
@Slf4j
public class QuickPdfTest {


    public static void main(String[] args) {
        try {
            log.info("=== 开始PDF转换性能测试 ===");

            List<String> strings = List.of("https://files.ybm100.com/INVT/Inquiry/20250817/pdf_12278378345914320527.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250817/pdf_10330417702874163628.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250817/pdf_13662094176562515114.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_10925002795156345437.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_15731793252425559475.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_15731793252425559475.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_15731793252425559475.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_6928034049999776198.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_15847290467151474877.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_14803763780959772907.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_16945173688565353784.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_5500737801684198708.pdf",
                "https://files.ybm100.com/INVT/Inquiry/20250818/pdf_15731793252425559475.pdf");

            //
            ExecutorService threadPool = Executors.newFixedThreadPool(20);
            CountDownLatch latch = new CountDownLatch(1000);

            final Random random = new Random();

            for (int i = 0; i < 1000; i++) {
                threadPool.execute(() -> {

                    try (InputStream inputStream = UrlConUtil.getStreamRetry("GET", strings.get(random.nextInt(strings.size())), 5000)) {
                        byte[] bytes = IOUtils.toByteArray(inputStream);

                        PdfBoxUtils.pdf2JpgOptimized(bytes);

                    } catch (Exception e) {
                        log.error("转换异常", e);
                    }
                    latch.countDown();
                });

                if (i % 2 == 0) {
                    log.info("已处理：{}", i);
                }
            }
            latch.await();
            log.info("=== 测试完成 ===");

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }


}
