package com.xyy.saas.localserver.medicare.dsl.protocol;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.net.MediaType;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.format.FormatType;
import com.xyy.saas.localserver.medicare.dsl.parse.JsonParameterParser;
import com.xyy.saas.localserver.medicare.dsl.parse.ObjectValueParser;
import com.xyy.saas.localserver.medicare.dsl.protocol.request.RequestBodyBuilderStrategy;
import com.xyy.saas.localserver.medicare.dsl.protocol.response.ProtocolResponse;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/07/25 15:56
 */
@Slf4j
@SuperBuilder
@ToString
public class HttpProtocalClient extends ProtocolClient {

    private static final ObjectValueParser valueParser = new JsonParameterParser();

    /**
     * @return
     */
    @Override
    public ProtocolClient toGenerateBody() {
        try {
            encryptValue();
            RequestBodyBuilderStrategy requestBodyBuilderStrategy = bodyStrategy.getClazz().getDeclaredConstructor().newInstance();
            String body = requestBodyBuilderStrategy.generateBody(inputObject);
            body = encryptBodyString(body);
            encryptBodyInHeader();
            inputObject.setBody(body);
            Map<String, String> commonHeaders = DSLContextHolder.getContext().getCommonHeader();
            if (MapUtil.isNotEmpty(commonHeaders)) {
                inputObject.addHeader(commonHeaders);
            }
        } catch (Exception e) {
            log.error("生成body数据失败,input:{}", inputObject);
            throw new RuntimeException("生成body数据失败,请联系开发");
        }
        return this;
    }


    /**
     * 加密header或者Body的某个key
     */
    private void encryptValue() {
        if (!inputObject.getContractConfig().isCommon()) {
            return;
        }
        ContractConfig.EncryptConfig encryptConfig = inputObject.getContractConfig().getCommonConfig().getEncrypt();
        if (encryptConfig == null) {
            return;
        }
        DSLContext context = DSLContextHolder.getContext();
        //加密header值
        Map<String, String> headerEncryptConfig = encryptConfig.getHeader();
        if (!CollectionUtils.isEmpty(headerEncryptConfig)) {
            headerEncryptConfig.forEach((k, v) -> {
                inputObject.getHeader().put(k, context.parseValue(v));
            });
        }
        //加密Body的某个key值
        Map<String, String> bodysEncryptConfig = encryptConfig.getBodys();
        if (!CollectionUtils.isEmpty(bodysEncryptConfig)) {
            bodysEncryptConfig.forEach((k, v) -> {
                inputObject.getBodys().put(k, context.parseValue(v));
            });
        }
    }

    /**
     * 加密整个body
     */
    private String encryptBodyString(String body) {
        if (!inputObject.getContractConfig().isCommon()) {
            return body;
        }
        ContractConfig.EncryptConfig encryptConfig = inputObject.getContractConfig().getCommonConfig().getEncrypt();
        if (encryptConfig == null) {
            return body;
        }
        if (StringUtils.hasLength(encryptConfig.getBody())) {
            inputObject.setBody(body);
            DSLContext context = DSLContextHolder.getContext();
            return context.parseValue(encryptConfig.getBody());
        }
        return body;
    }

    private void encryptBodyInHeader() {
        if (!inputObject.getContractConfig().isCommon()) {
            return;
        }
        ContractConfig.EncryptConfig encryptConfig = inputObject.getContractConfig().getCommonConfig().getEncrypt();
        if (encryptConfig == null) {
            return;
        }
        DSLContext context = DSLContextHolder.getContext();
        Map<String, String> bodysEncryptInHeaderConfig = encryptConfig.getBodyInHeader();
        if (!CollectionUtils.isEmpty(bodysEncryptInHeaderConfig)) {
            bodysEncryptInHeaderConfig.forEach((k, v) -> {
                inputObject.getHeader().put(k, context.parseValue(v));
            });
        }
    }

    /**
     * @return
     */
    @Override
    public ProtocolResponse toInvoker() {
        ContractConfig.FunctionConfig functionConfig = this.inputObject.getFunctionConfig();
        String url = valueParser.parseValue(functionConfig.getDomain(), DSLContextHolder.getContext()) + valueParser.parseValue(functionConfig.getPath(), DSLContextHolder.getContext());
        int timeout = functionConfig.getTimeout() * 1000;
        Map<String, String> header = this.inputObject.getHeader();
        Method method = functionConfig.getRequest().getMethod();
        HttpRequest httpRequest = null;
        switch (method) {
            case GET -> {
                httpRequest = HttpUtil.createGet(url).addHeaders(header).timeout(timeout);
            }
            case POST -> {
                httpRequest = HttpUtil.createPost(url).addHeaders(header).body(inputObject.getBody()).timeout(timeout);
            }
            default -> {
                log.error("FunctionConfig‘method is errer!  method:{}, functionConfig:{}", method, functionConfig);
                return null;
            }
        }
        FormatType type = functionConfig.getRequest().getFormat();
        if (type != null) {
            switch (type) {
                case form -> {

                    httpRequest.contentType(MediaType.FORM_DATA.toString());
//                httpRequest.contentType(MediaType.FORM_DATA.toString());
                    httpRequest = httpRequest.form(inputObject.getBodys());
                }
                default -> {
                }
            }
        }
        HttpResponse httpResponse = httpRequest.execute();

        ProtocolResponse response = ProtocolResponse.builder().code(httpResponse.isOk() ? 0 : -1).body(httpResponse.body()).build();
        String responseBody = httpResponse.body();


//        if ("9001".equals(inputObject.getFunctionConfig().getRequest().getBody().get("infno"))) {
//            responseBody = "{\"infcode\":\"0\",\"warn_msg\":null,\"cainfo\":\"MY0TlHiiEU6fyR/7VAmV7Vh1OX6S7dHPMz/8Wx5ue3lXeK1ZkaR3BUKA/rkuI2ZKlQnIl7r09TfUM4C4h5ZfHQ==\",\"err_msg\":\"成功\",\"refmsg_time\":\"20231010194535638\",\"signtype\":\"SM2\",\"respond_time\":\"20231010194535697\",\"inf_refmsgid\":\"220000202310101945354759240122\",\"output\":\"YiD4lhV1p1d9jkfO4rx/Q68gO8uHF5fJauCP3aI3zhiC2yO2Oe0XiBdoc9kHaZ5IvUdB9/lDv6zvotFPrGvuj2PQ7FSvH3PvVXz9xckKeBfKMASje3aB4CYgPl62qFWr\"}";
//        }
        //解析返回参数成Map

        String body = decryptBodyString(responseBody);
        JSONObject resultMap = JSONUtil.parseObj(body);
        decryptValue(resultMap);
        response.setResultMap(resultMap);
        return response;
    }

    /**
     * 对Body的某个key进行解密
     */
    private void decryptValue(Map<String, Object> resultMap) {
        if (!inputObject.getContractConfig().isCommon()) {
            return;
        }
        ContractConfig.DecryptConfig decryptConfig = inputObject.getContractConfig().getCommonConfig().getDecrypt();
        if (decryptConfig == null) {
            return;
        }
        //解密Body的某个key值
        Map<String, String> bodysDecryptConfig = decryptConfig.getBodys();
        if (!CollectionUtils.isEmpty(bodysDecryptConfig)) {
            DSLContext context = DSLContextHolder.getContext();
            ContractConfig.FunctionConfig functionConfig = this.inputObject.getFunctionConfig();
            bodysDecryptConfig.forEach((field, expression) -> {
                Object responseObject = resultMap.get(field);
                if (responseObject instanceof String s) {
                    String value = context.parseValue(expression, s);
//                    Object configObject = functionConfig.getResponse().get(field);
//                    if (configObject instanceof Map<?,?>) {
//                        ;
                    resultMap.put(field, JSONUtil.parseObj(value));
//                    }
//                    resultMap.put(field, );
                }
            });
        }
    }

    /**
     * 解密整个body
     */
    private String decryptBodyString(String body) {
        if (!inputObject.getContractConfig().isCommon()) {
            return body;
        }
        ContractConfig.DecryptConfig decryptConfig = inputObject.getContractConfig().getCommonConfig().getDecrypt();
        if (decryptConfig == null) {
            return body;
        }
        if (StringUtils.hasLength(decryptConfig.getBody())) {
            DSLContext context = DSLContextHolder.getContext();
            return context.parseValue(decryptConfig.getBody());
        }
        return body;
    }

    /**
     *
     */
    @Override
    public void parseResponseBody() {

    }


//    @Builder
//    public HttpProtocalClient(InputObject inputObject) {
//        super(inputObject);
//    }


}
