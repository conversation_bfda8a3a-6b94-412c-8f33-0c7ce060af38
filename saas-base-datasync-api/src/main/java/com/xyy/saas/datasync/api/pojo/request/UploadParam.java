package com.xyy.saas.datasync.api.pojo.request;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 数据上传参数
 */
public class UploadParam implements Serializable {
    private String bizLine;
    private String organSign;
    private List<UploadData> data;
    private String remark;

    /**
     * 数据校验
     */
    public void check() {
        if(StringUtils.isEmpty(bizLine)) {
            throw new RuntimeException("[bizLine]业务线为空");
        }

        if(StringUtils.isEmpty(organSign)) {
            throw new RuntimeException("[organSign]机构号为空");
        }

        if(data == null) {
            throw new RuntimeException("[data]同步数据为空");
        }

        data.forEach(UploadData::check);
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public List<UploadData> getData() {
        return data;
    }

    public void setData(List<UploadData> data) {
        this.data = data;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
