package com.xyy.saas.datasync.api.pojo.response;

import com.xyy.saas.datasync.api.pojo.request.Pagination;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class BaseDownloadAllResp implements Serializable {
    /**
     * 数据集
     */
    private List<Object> data;
    /**
     * 当前页最大主键ID
     */
    private Long maxTableId;
    /**
     * 当前页最大更新时间
     */
    private Date maxUpdateTime;
    /**
     * 分页信息
     */
    @Deprecated
    private Pagination pagination;

    public List<Object> getData() {
        return data;
    }

    public void setData(List<Object> data) {
        this.data = data;
    }

    public Pagination getPagination() {
        return pagination;
    }

    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }

    public Long getMaxTableId(){
        return maxTableId;
    }

    public void setMaxTableId(Long maxTableId){
        this.maxTableId = maxTableId;
    }

    public Date getMaxUpdateTime(){
        return maxUpdateTime;
    }

    public void setMaxUpdateTime(Date maxUpdateTime){
        this.maxUpdateTime = maxUpdateTime;
    }
}
