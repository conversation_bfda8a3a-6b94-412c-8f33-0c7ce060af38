package com.xyy.saas.datasync.api.pojo.request;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

public class BaseDownloadAllParam implements Serializable {
    /**
     * 机构号
     */
    private String organSign;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 分页信息(旧分页信息,废弃)
     */
    @Deprecated
    private Pagination pagination;
    /**
     * 业务表ID
     */
    private Long tableId;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 页大小
     */
    private Integer pageSize;

    public void check() {
        if(StringUtils.isEmpty(organSign)) {
            throw new RuntimeException("[organSign]机构号为空");
        }

        if(StringUtils.isEmpty(tableName)) {
            throw new RuntimeException("[tableName]表名为空");
        }

        if (tableId == null) {
            // 普通分页逻辑
            if(pagination == null) {
                throw new RuntimeException("[pagination]分页信息为空");
            }

            if(pagination.getCurrentPage() == null) {
                throw new RuntimeException("[pagination.currentPage]当前页为空");
            }
        } else {
            // 用表主键ID做游标进行分页
            if (pageSize == null || pageSize <= 0) {
                pageSize = Pagination.DEFAULT_PAGE_SIZE;
            }
        }
        if (updateTime != null) {
            if (pageSize == null || pageSize <= 0) {
                pageSize = Pagination.DEFAULT_PAGE_SIZE;
            }
        }
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Pagination getPagination() {
        return pagination;
    }

    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }

    public Long getTableId(){
        return tableId;
    }

    public void setTableId(Long tableId){
        this.tableId = tableId;
    }

    public Integer getPageSize(){
        return pageSize;
    }

    public void setPageSize(Integer pageSize){
        this.pageSize = pageSize;
    }

    public Date getUpdateTime(){
        return updateTime;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }
}
