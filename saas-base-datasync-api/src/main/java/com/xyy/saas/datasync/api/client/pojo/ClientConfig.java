package com.xyy.saas.datasync.api.client.pojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 爬虫插件配置
 */
public class ClientConfig implements Serializable {
    private Long id;
    private String bizLine;
    private Long outUniqueId;
    private String remark;
    private String config;
    private Date updateTime;
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public Long getOutUniqueId() {
        return outUniqueId;
    }

    public void setOutUniqueId(Long outUniqueId) {
        this.outUniqueId = outUniqueId;
    }

    public String getRemark(){
        return remark;
    }

    public void setRemark(String remark){
        this.remark = remark;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
