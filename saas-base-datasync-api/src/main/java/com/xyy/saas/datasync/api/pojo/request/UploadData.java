package com.xyy.saas.datasync.api.pojo.request;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class UploadData implements Serializable {
    private String tableName;
    private String eventType;
    private Object data;

    /**
     * 数据校验
     */
    public void check() {
        if(StringUtils.isEmpty(tableName)) {
            throw new RuntimeException("[tableName]表名为空");
        }

        if(StringUtils.isEmpty(eventType)) {
            throw new RuntimeException("[syncType]事件类型为空");
        }
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
