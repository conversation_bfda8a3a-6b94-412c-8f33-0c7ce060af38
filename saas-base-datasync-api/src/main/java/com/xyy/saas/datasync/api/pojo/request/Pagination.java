package com.xyy.saas.datasync.api.pojo.request;

import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;

public class Pagination implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 默认页数
     */
    public static final Integer DEFAULT_MIN_PAGE_NUM = 1;
    /**
     * 默认页大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 100;

    /**
     * 当前页
     */
    private Integer currentPage = DEFAULT_MIN_PAGE_NUM;
    /**
     * 页大小
     */
    private Integer pageSize = DEFAULT_PAGE_SIZE;
    /**
     * 总数
     */
    private Integer total;

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    /**
     * 计算起始行数
     * @param currentPage
     * @param pageSize
     * @return
     */
    public static Integer offset(Integer currentPage, Integer pageSize) {
        if(currentPage == null || currentPage < DEFAULT_MIN_PAGE_NUM) {
            currentPage = DEFAULT_MIN_PAGE_NUM;
        }

        if(pageSize == null || pageSize <= NumberUtils.INTEGER_ZERO) {
            pageSize = DEFAULT_PAGE_SIZE;
        }

        return (currentPage - 1) * pageSize;
    }
}
