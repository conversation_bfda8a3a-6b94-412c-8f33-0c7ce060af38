package com.xyy.saas.datasync.api.hyt.pojo;

import java.io.Serializable;

/**
 * 荷叶通ERP数据抓取配置
 * <AUTHOR>
 * @date 2021/5/10
 */
public class HytClientConfig implements Serializable {
    private static final long serialVersionUID = 6951528122389130173L;
    /**
     * ERP软件ID
     */
    private Long softwareId;
    /**
     * ERP软件名称
     */
    private String softwareName;
    /**
     * 任务抓取Json数据
     */
    private String configJson;

    public Long getSoftwareId(){
        return softwareId;
    }

    public void setSoftwareId(Long softwareId){
        this.softwareId = softwareId;
    }

    public String getSoftwareName(){
        return softwareName;
    }

    public void setSoftwareName(String softwareName){
        this.softwareName = softwareName;
    }

    public String getConfigJson(){
        return configJson;
    }

    public void setConfigJson(String configJson){
        this.configJson = configJson;
    }
}
