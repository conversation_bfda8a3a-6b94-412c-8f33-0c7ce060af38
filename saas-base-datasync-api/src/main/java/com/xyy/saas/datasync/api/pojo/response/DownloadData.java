package com.xyy.saas.datasync.api.pojo.response;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class DownloadData implements Serializable {
    /**
     * 机构号
     */
    private String organSign;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 事件类型
     */
    private String eventType;
    /**
     * 数据
     */
    private Object data;
    /**
     * 备注
     */
    private String remark;

    /**
     * 构建对象
     * @param organSign
     * @param tableName
     * @param data
     * @return
     */
    public static DownloadData build(String organSign, String tableName, Object data) {
        return build(organSign, tableName, StringUtils.EMPTY, data);
    }

    /**
     * 构建对象
     * @param organSign
     * @param tableName
     * @param eventType
     * @param data
     * @return
     */
    public static DownloadData build(String organSign, String tableName, String eventType, Object data) {
        DownloadData downloadData = new DownloadData();
        downloadData.setOrganSign(organSign);
        downloadData.setTableName(tableName);
        downloadData.setEventType(eventType);
        downloadData.setData(data);
        return downloadData;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
