package com.xyy.saas.datasync.spi;

import com.xyy.saas.datasync.api.pojo.request.BaseDownloadAllParam;
import com.xyy.saas.datasync.api.pojo.response.BaseDownloadAllResp;

import java.util.List;
import java.util.Map;

/**
 * 业务数据同步SPI
 */
public interface DataSyncAdapter {
    /**
     * (此接口已废弃)根据父机构获取子机构/根据机构获取通知机构
     * @param organSign
     * @return
     */
    @Deprecated
    List<String> listSubOrganSign(String organSign);

    /**
     * 根据表名和ID，查询数据列表
     * @param table
     * @param ids
     * @return
     */
    Map<Long, Object> listDataByIds(String table, List<Long> ids);

    /**
     * 分页查询(根据主键ID偏移分页)
     * @param param
     * @return
     */
    BaseDownloadAllResp listData(BaseDownloadAllParam param);

    /**
     * 分页查询(大于updateTime) <br> updateTime > #{updateTime} LIMIT #{pageSize}
     * @param param
     * @return
     */
    BaseDownloadAllResp listDataByUpdateTime(BaseDownloadAllParam param);

    /**
     * 查询数据(等于updateTime) <br> updateTime = #{updateTime}
     * @param param
     * @return
     */
    BaseDownloadAllResp listDataEqualUpdateTime(BaseDownloadAllParam param);
}
