package com.xyy.saas.datasync.api.pojo.request;

import org.apache.commons.lang3.StringUtils;

public class DownloadAllParam extends BaseDownloadAllParam {
    /**
     * 业务线标识
     */
    private String bizLine;

    @Override
    public void check() {
        super.check();

        if(StringUtils.isEmpty(bizLine)) {
            throw new RuntimeException("[bizLine]业务线为空");
        }
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }
}
