package com.xyy.saas.datasync.api.pojo.request;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

public class LoopCheckParam implements Serializable {
    private String bizLine;
    private String organSign;
    private List<LoopCheckData> data;

    public static void check(LoopCheckParam param) {
        if(param == null) {
            throw new RuntimeException("参数为空");
        }

        if(StringUtils.isEmpty(param.getBizLine())) {
            throw new RuntimeException("[bizLine]业务线为空");
        }

        if(StringUtils.isEmpty(param.getOrganSign())) {
            throw new RuntimeException("[organSign]机构号为空");
        }

        if(param.getData() == null) {
            throw new RuntimeException("参数为空");
        }

        param.getData().forEach(LoopCheckData::check);
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public List<LoopCheckData> getData() {
        return data;
    }

    public void setData(List<LoopCheckData> data) {
        this.data = data;
    }
}
