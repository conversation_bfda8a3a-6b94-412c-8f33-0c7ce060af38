package com.xyy.saas.datasync.api.enums;

/**
 * 任务类型枚举
 */
public enum TaskTypeEnum {
    INCREMENT(1, "增量任务"),
    FULL(2, "全量任务"),
    MIXED(3, "混合任务"),
    GROUP(4, "增量分组任务"),
    ;

    private Integer type;
    private String desc;

    TaskTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 根据type获取枚举
     * @param type
     * @return
     */
    public static TaskTypeEnum getType(Integer type) {
        if(type == null) {
            return null;
        }

        for(TaskTypeEnum typeEnum : values()) {
            if(typeEnum.type.equals(type)) {
                return typeEnum;
            }
        }

        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
