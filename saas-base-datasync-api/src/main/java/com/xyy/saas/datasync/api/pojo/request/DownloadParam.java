package com.xyy.saas.datasync.api.pojo.request;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

public class DownloadParam implements Serializable {
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 机构号
     */
    private String organSign;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 客户端当前版本号
     */
    private Long currentVersion;
    /**
     * 客户端当前表最大更新时间
     */
    private Date updateTime;
    /**
     * 页大小，非必填
     */
    private Integer pageSize;

    /**
     * 数据校验
     */
    public void check() {
        if(StringUtils.isEmpty(bizLine)) {
            throw new RuntimeException("[bizLine]业务线为空");
        }

        if(StringUtils.isEmpty(organSign)) {
            throw new RuntimeException("[organSign]机构号为空");
        }

        if(StringUtils.isEmpty(tableName)) {
            throw new RuntimeException("[tableName]表名为空");
        }

        if(currentVersion == null && updateTime == null) {
            throw new RuntimeException("[currentVersion]当前版本号和[updateTime]当前最大更新时间不能同时为空");
        }
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getCurrentVersion() {
        return currentVersion;
    }

    public void setCurrentVersion(Long currentVersion) {
        this.currentVersion = currentVersion;
    }

    public Date getUpdateTime(){
        return updateTime;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
