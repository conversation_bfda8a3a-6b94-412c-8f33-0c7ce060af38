package com.xyy.saas.datasync.api;

import com.xyy.saas.datasync.api.pojo.Result;
import com.xyy.saas.datasync.api.pojo.request.*;
import com.xyy.saas.datasync.api.pojo.response.*;

/**
 * 客户端下载数据服务
 */
public interface DownloadAPi {
    /**
     * 下载增量数据
     * @param param
     * @return
     */
    Result<DownloadResp> pullDataByVersion(DownloadParam param);

    /**
     * 下载全量数据
     * @param param
     * @return
     */
    Result<DownloadAllResp> pullDataAll(DownloadAllParam param);

    /**
     * 客户端轮训补偿监测
     * @param param
     * @return
     */
    Result<LoopCheckResp> loopCheck(LoopCheckParam param);
}
