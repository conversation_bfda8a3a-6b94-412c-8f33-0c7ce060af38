package com.xyy.saas.datasync.api.pojo.response;

import com.xyy.saas.datasync.api.pojo.request.LoopCheckData;
import java.io.Serializable;
import java.util.List;

public class LoopCheckResp implements Serializable {
    private String bizLine;
    private String organSign;
    private List<LoopCheckData> data;

    /**
     * 构建对象
     * @param bizLine
     * @param organSign
     * @param data
     * @return
     */
    public static LoopCheckResp build(String bizLine, String organSign, List<LoopCheckData> data) {
        LoopCheckResp resp = new LoopCheckResp();
        resp.setBizLine(bizLine);
        resp.setOrganSign(organSign);
        resp.setData(data);
        return resp;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public List<LoopCheckData> getData() {
        return data;
    }

    public void setData(List<LoopCheckData> data) {
        this.data = data;
    }
}
