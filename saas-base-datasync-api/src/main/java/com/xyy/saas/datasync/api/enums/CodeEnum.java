package com.xyy.saas.datasync.api.enums;

public enum CodeEnum {
    ERROR(-1, "失败"),
    SUCCESS(0, "成功"),
    TOKEN_EXPIRED(9009, "Token过期"),
    ;

    CodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
