package com.xyy.saas.datasync.api.client.pojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 爬虫插件实例
 */
public class ClientInstance implements Serializable {
    private Long id;
    private String bizLine;
    private Long outUniqueId;
    private String clientType;
    private Long clientConfigId;
    private Long clientId;
    private String clientKey;
    private String instanceConfig;
    private Date updateTime;
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public Long getOutUniqueId() {
        return outUniqueId;
    }

    public void setOutUniqueId(Long outUniqueId) {
        this.outUniqueId = outUniqueId;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public Long getClientConfigId() {
        return clientConfigId;
    }

    public void setClientConfigId(Long clientConfigId) {
        this.clientConfigId = clientConfigId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getClientKey() {
        return clientKey;
    }

    public void setClientKey(String clientKey) {
        this.clientKey = clientKey;
    }

    public String getInstanceConfig() {
        return instanceConfig;
    }

    public void setInstanceConfig(String instanceConfig) {
        this.instanceConfig = instanceConfig;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
