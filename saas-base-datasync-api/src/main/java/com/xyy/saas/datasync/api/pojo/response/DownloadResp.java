package com.xyy.saas.datasync.api.pojo.response;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class DownloadResp implements Serializable {
    /**
     * 是否还有数据
     */
    private Boolean hasData;
    /**
     * 该次拉取最大版本号
     */
    private Long maxVersion;
    /**
     * 最大版本号对应数据更新时间
     */
    @Deprecated
    private Date updateTime;
    /**
     * 最大版本号对应数据更新时间
     */
    private Long maxUpdateTime;
    /**
     * 数据集
     */
    private List<DownloadData> data;
    /**
     * 是否需要全量同步(客户端版本过低时,需要全量同步(false:否; true:是))
     */
    private Boolean needFullSync = Boolean.FALSE;

    public Boolean getHasData() {
        return hasData;
    }

    public void setHasData(Boolean hasData) {
        this.hasData = hasData;
    }

    public Long getMaxVersion() {
        return maxVersion;
    }

    public void setMaxVersion(Long maxVersion) {
        this.maxVersion = maxVersion;
    }

    public Date getUpdateTime(){
        return updateTime;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Long getMaxUpdateTime(){
        return maxUpdateTime;
    }

    public void setMaxUpdateTime(Long maxUpdateTime){
        this.maxUpdateTime = maxUpdateTime;
    }

    public List<DownloadData> getData() {
        return data;
    }

    public void setData(List<DownloadData> data) {
        this.data = data;
    }

    public Boolean getNeedFullSync(){
        return needFullSync;
    }

    public void setNeedFullSync(Boolean needFullSync){
        this.needFullSync = needFullSync;
    }
}
