package com.xyy.saas.datasync.api.pojo.response;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class DownloadAllResp implements Serializable {
    /**
     * 是够有数据
     */
    private Boolean hasData;
    /**
     * 当前页
     */
    private Integer currentPage;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 最大版本号
     */
    private Long maxVersion;
    /**
     * 最大版本号对应数据最后一次更新时间
     */
    @Deprecated
    private Date updateTime;
    /**
     * 最大版本号对应数据最后一次更新时间
     */
    private Long maxUpdateTime;
    /**
     * 业务表最大ID
     */
    private Long maxTableId;

    /**
     * 数据集
     */
    private List<DownloadData> data;

    public Boolean getHasData() {
        return hasData;
    }

    public void setHasData(Boolean hasData) {
        this.hasData = hasData;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getMaxVersion() {
        return maxVersion;
    }

    public void setMaxVersion(Long maxVersion) {
        this.maxVersion = maxVersion;
    }

    public List<DownloadData> getData() {
        return data;
    }

    public void setData(List<DownloadData> data) {
        this.data = data;
    }


    public Long getMaxTableId(){
        return maxTableId;
    }

    public void setMaxTableId(Long maxTableId){
        this.maxTableId = maxTableId;
    }

    public Date getUpdateTime(){
        return updateTime;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Long getMaxUpdateTime(){
        return maxUpdateTime;
    }

    public void setMaxUpdateTime(Long maxUpdateTime){
        this.maxUpdateTime = maxUpdateTime;
    }
}
