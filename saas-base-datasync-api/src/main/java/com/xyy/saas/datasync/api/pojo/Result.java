package com.xyy.saas.datasync.api.pojo;

import com.xyy.saas.datasync.api.enums.CodeEnum;

import java.io.Serializable;

/**
 * 统一相应体
 * @param <T>
 */
public class Result<T> implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String msg;
    /**
     * 结果集
     */
    private T result;

    public Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(Integer code, String msg, T result) {
        this.code = code;
        this.msg = msg;
        this.result = result;
    }

    /**
     * 是够成功
     * @return
     */
    public boolean isSuccess() {
        return CodeEnum.SUCCESS.getCode().equals(getCode());
    }

    /**
     * 成功
     * @param result
     * @param <T>
     * @return
     */
    public static <T> Result success(T result) {
        return new Result(CodeEnum.SUCCESS.getCode(), CodeEnum.SUCCESS.getMsg(), result);
    }

    /**
     * 成功
     * @return
     */
    public static Result success() {
        return new Result(CodeEnum.SUCCESS.getCode(), CodeEnum.SUCCESS.getMsg());
    }

    /**
     * 失败
     * @param msg
     * @return
     */
    public static Result fail(String msg) {
        return new Result(CodeEnum.ERROR.getCode(), msg);
    }

    /**
     * 失败
     * @param code
     * @param msg
     * @return
     */
    public static Result fail(Integer code, String msg) {
        return new Result(code, msg);
    }

    /**
     * 失败
     * @return
     */
    public static Result fail() {
        return new Result(CodeEnum.ERROR.getCode(), CodeEnum.ERROR.getMsg());
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }
}
