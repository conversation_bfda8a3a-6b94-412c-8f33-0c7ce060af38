package com.xyy.saas.datasync.api.pojo.request;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class LoopCheckData implements Serializable {
    private String tableName;
    /**
     * 当前最大版本号
     * 只用于请求入参时使用. 响应出参不再使用此字段 2020-12-16
     */
    private Long currentVersion;
    private Boolean hasData;
    /**
     * 是否新表(true:是 false:否)-用于查询返回
     */
    private Boolean newTable = false;

    public static void check(LoopCheckData data) {
        if(data == null) {
            throw new RuntimeException("参数为空");
        }

        if(StringUtils.isEmpty(data.getTableName())) {
            throw new RuntimeException("[tableName]表名为空");
        }

        if(data.getCurrentVersion() == null) {
            throw new RuntimeException("[currentVersion]当前版本号为空");
        }
    }

    /**
     * 构建对象
     * @param tableName
     * @param currentVersion
     * @param hasData
     * @return
     */
    public static LoopCheckData build(String tableName, Long currentVersion, Boolean hasData, Boolean newTable) {
        LoopCheckData loopCheckData = new LoopCheckData();
        loopCheckData.setTableName(tableName);
        loopCheckData.setCurrentVersion(currentVersion);
        loopCheckData.setHasData(hasData);
        loopCheckData.setNewTable(newTable);
        return loopCheckData;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getCurrentVersion() {
        return currentVersion;
    }

    public void setCurrentVersion(Long currentVersion) {
        this.currentVersion = currentVersion;
    }

    public Boolean getHasData() {
        return hasData;
    }

    public void setHasData(Boolean hasData) {
        this.hasData = hasData;
    }

    public Boolean getNewTable(){
        return newTable;
    }

    public void setNewTable(Boolean newTable){
        this.newTable = newTable;
    }
}
