package com.xyy.saas.datasync.api.client.pojo;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 数据上传
 */
public class UploadParam implements Serializable {
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 企业ID
     */
    private Long outUniqueId;
    /**
     * 任务ID
     */
    private Integer taskId;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 数据集
     */
    private Object data;
    /**
     * 当前偏移量
     */
    private String currentOffset;
    /**
     * 首次运行
     */
    private Boolean firstRun;
    /**
     * 上传时间戳
     */
    private Long timestamp;
    /**
     * 商户ID
     */
    private Long merchantId;
    /**
     * ERP绑定实例
     */
    private Long erpRelationId;

    /**
     * 校验
     * @param uploadParam
     */
    public static void check(UploadParam uploadParam) {
        if(uploadParam == null) {
            throw new RuntimeException("UploadParam is null !!!!");
        }

        if(StringUtils.isEmpty(uploadParam.getBizLine())) {
            throw new RuntimeException("bizLine param is null !!!!");
        }
        if(uploadParam.getOutUniqueId() == null) {
            throw new RuntimeException("outUniqueId param is null !!!!");
        }
        if(uploadParam.getTaskId() == null) {
            throw new RuntimeException("taskId param is null !!!!");
        }
        if(StringUtils.isEmpty(uploadParam.getTableName())) {
            throw new RuntimeException("tableName param is null !!!!");
        }
        if(uploadParam.getData() == null) {
            throw new RuntimeException("data param is null !!!!");
        }

        if(uploadParam.getTimestamp() == null) {
            uploadParam.setTimestamp(System.currentTimeMillis());
        }
    }

    public String getBizLine() {
        return bizLine;
    }

    public void setBizLine(String bizLine) {
        this.bizLine = bizLine;
    }

    public Long getOutUniqueId() {
        return outUniqueId;
    }

    public void setOutUniqueId(Long outUniqueId) {
        this.outUniqueId = outUniqueId;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getCurrentOffset() {
        return currentOffset;
    }

    public void setCurrentOffset(String currentOffset) {
        this.currentOffset = currentOffset;
    }

    public Boolean getFirstRun() {
        return firstRun;
    }

    public void setFirstRun(Boolean firstRun) {
        this.firstRun = firstRun;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Long getMerchantId(){
        return merchantId;
    }

    public void setMerchantId(Long merchantId){
        this.merchantId = merchantId;
    }

    public Long getErpRelationId(){
        return erpRelationId;
    }

    public void setErpRelationId(Long erpRelationId){
        this.erpRelationId = erpRelationId;
    }
}
