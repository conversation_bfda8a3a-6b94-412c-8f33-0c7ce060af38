package com.xyy.saas.datasync.api.client;

import com.xyy.saas.datasync.api.client.pojo.ClientConfig;
import com.xyy.saas.datasync.api.client.pojo.ClientInstance;
import com.xyy.saas.datasync.api.client.pojo.UploadParam;
import com.xyy.saas.datasync.api.pojo.Result;

/**
 * 爬虫插件客户端服务
 */
public interface CrawlerPluginApi {
    /**
     * 根据外部ID查询配置信息
     * @param bizLine      业务线
     * @param outUniqueId  唯一ID(业务平台ID)
     * @return
     */
    Result<ClientConfig> getConfig(String bizLine, Long outUniqueId);

    /**
     * 根据外部ID查询实例信息
     * @param bizLine      业务线
     * @param outUniqueId  企业ID
     * @return
     */
    Result<ClientInstance> getInstance(String bizLine, Long outUniqueId);

    /**
     * 根据客户端id，客户端key 获取实例
     * @param clientId
     * @param clientKey
     * @return
     */
    Result<ClientInstance> getClientInstance(Long clientId, String clientKey);

    /**
     * 抓取数据上传
     * @param param
     * @return
     */
    Result upload(UploadParam param);

    /**
     * 根据荷叶通账号获取实例
     * @param hytAcount
     * @return
     */
    Result<ClientInstance> getHytClientInstance(String hytAcount);

    /**
     * 新增/更新ClientConfig
     * @param clientConfig
     * @return
     */
//    Result<Boolean> upsertClientConfig(ClientConfig clientConfig);

    /**
     * 新增/更新ClientInstance
     * @param clientInstance
     * @return
     */
//    Result<Boolean> upsertClientInstance(ClientInstance clientInstance);

    /**
     * 删除N个月前的Event数据
     * @param month
     * @param pageSize
     * @return
     */
    Result<Boolean> deleteOldEvent(int month, int pageSize);
}
