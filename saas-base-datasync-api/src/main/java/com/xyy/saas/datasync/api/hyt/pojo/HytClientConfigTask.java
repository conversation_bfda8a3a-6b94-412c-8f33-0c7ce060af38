package com.xyy.saas.datasync.api.hyt.pojo;

/**
 * <AUTHOR>
 * @date 2021/5/10
 */
public class HytClientConfigTask {
    /**
     * 任务ID
     */
    private Integer taskId;
    /**
     * 任务类型 com.xyy.saas.datasync.api.enums.TaskTypeEnum
     */
    private Integer taskType;
    /**
     * 业务线
     */
    private String bizLine;
    /**
     * 企业ID
     */
    private Long outUniqueId;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 任务执行间隔(秒)
     */
    private Integer interval;
    /**
     * 任务
     */
    private String task;
    /**
     * 增量游标字段 | 增量抓取模式任务有意义
     */
    private String cursorField;
    /**
     * 增量游标偏移量 | 增量抓取模式任务有意义
     */
    private Object cursorOffset;
    /**
     * 全量排序字段 | 全量抓取模式有意义
     */
    private String orderBy;
    /**
     * Group任务，分组字段
     */
    private String groupBy;
    /**
     * 时候为首次运行 | 混合抓取模式有意义
     */
    private Boolean firstRun = Boolean.TRUE;
    /**
     * 页大小
     */
    private Integer pageSize;

    public Integer getTaskId(){
        return taskId;
    }

    public void setTaskId(Integer taskId){
        this.taskId = taskId;
    }

    public Integer getTaskType(){
        return taskType;
    }

    public void setTaskType(Integer taskType){
        this.taskType = taskType;
    }

    public String getBizLine(){
        return bizLine;
    }

    public void setBizLine(String bizLine){
        this.bizLine = bizLine;
    }

    public Long getOutUniqueId(){
        return outUniqueId;
    }

    public void setOutUniqueId(Long outUniqueId){
        this.outUniqueId = outUniqueId;
    }

    public String getTableName(){
        return tableName;
    }

    public void setTableName(String tableName){
        this.tableName = tableName;
    }

    public Integer getInterval(){
        return interval;
    }

    public void setInterval(Integer interval){
        this.interval = interval;
    }

    public String getTask(){
        return task;
    }

    public void setTask(String task){
        this.task = task;
    }

    public String getCursorField(){
        return cursorField;
    }

    public void setCursorField(String cursorField){
        this.cursorField = cursorField;
    }

    public Object getCursorOffset(){
        return cursorOffset;
    }

    public void setCursorOffset(Object cursorOffset){
        this.cursorOffset = cursorOffset;
    }

    public String getOrderBy(){
        return orderBy;
    }

    public void setOrderBy(String orderBy){
        this.orderBy = orderBy;
    }

    public String getGroupBy(){
        return groupBy;
    }

    public void setGroupBy(String groupBy){
        this.groupBy = groupBy;
    }

    public Boolean getFirstRun(){
        return firstRun;
    }

    public void setFirstRun(Boolean firstRun){
        this.firstRun = firstRun;
    }

    public Integer getPageSize(){
        return pageSize;
    }

    public void setPageSize(Integer pageSize){
        this.pageSize = pageSize;
    }
}
