package com.xyy.saas.datasync.api.hyt;

import com.xyy.saas.datasync.api.hyt.pojo.HytClientConfig;
import com.xyy.saas.datasync.api.hyt.pojo.HytClientInstance;
import com.xyy.saas.datasync.api.pojo.Result;

/**
 * 荷叶通配置接口
 * <AUTHOR>
 * @date 2021/5/10
 */
public interface HytConfigApi {
    /**
     * 根据ERP软件ID查询荷叶通ClientConfig
     * @param erpSoftwareId
     * @return
     */
    Result<HytClientConfig> queryErpConfig(Long erpSoftwareId);

    /**
     * 更新荷叶通ClientConfig
     * @param hytClientConfig
     * @return
     */
    Result<Boolean> upsertConfig(HytClientConfig hytClientConfig);

    /**
     * 更新荷叶通ClientInstance
     * @param hytClientInstance
     * @return
     */
    Result<Boolean> upsertInstance(HytClientInstance hytClientInstance);
}
