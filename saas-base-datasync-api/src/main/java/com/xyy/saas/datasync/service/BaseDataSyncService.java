package com.xyy.saas.datasync.service;

import com.xyy.saas.datasync.api.pojo.response.BaseDownloadAllResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用查询服务工具
 * <AUTHOR>
 * @date 2021/3/2
 */
@Component
public class BaseDataSyncService {
    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;
    final String DEFAULT_ORGAN_COL_NAME = "organ_sign";
    final String DEFAULT_UPDATE_TIME_COL_NAME = "update_time";
    final String DEFAULT_ID_COL_NAME = "id";
    String listDataByIdsSql = "select * from  {0} where id in ({1} ) ";
    String listDataSql ="select * from  {0} where id > {1} {2}  order by id asc limit {3}" ;//{2} 为“organSign=XX"
    String listDataByUpdateTimeSql ="select * from  {0} where update_time > ? {1}  order by update_time asc limit {2}" ;//{3} 为“organSign=XX"
    String listDataEqualUpdateTimeSql ="select * from  {0} where update_time = ? {1} " ;//{1} 为“organSign=XX"
    //不同表的机构列名可能不一样，不是用的organ_sign需要配置到map中
    Map<String,String> organColumNameMap = new HashMap<>();
    //有的表没有updateTime，不支持降级
    Set<String> noUpdateTimeTables = new HashSet<>();

    public BaseDataSyncService() {
        init();
    }

    /**
     * 根据表名，id集合查询
     * @param tableName
     * @param list
     * @return
     */
    public Map<Long, Object> listDataByIds(String tableName, List<Long> list) {
        if(StringUtils.isEmpty(tableName) || CollectionUtils.isEmpty(list)){
            return new HashMap<>();
        }
        Map<Long, Object> result = new HashMap<>();
        String ids = list.stream().map(String::valueOf).collect(Collectors.joining(","));
        String sql = MessageFormat.format(listDataByIdsSql,tableName,ids);
        List<Map<String,Object>> queryResults = jdbcTemplate.queryForList(sql);
        if(!CollectionUtils.isEmpty(queryResults)){
            for(Map<String,Object> row : queryResults){
                result.put( Long.valueOf( row.get(DEFAULT_ID_COL_NAME).toString()),toJaveNameMap(row));//
            }
        }
        return result;
    }

    /**
     * 查询从id开始的n条数据
     * @param tableName
     * @param tableId
     * @param organSign
     * @param pageSize
     * @return
     */
    public BaseDownloadAllResp listData(String tableName,Long tableId ,String organSign,Integer pageSize){
        BaseDownloadAllResp baseDownloadAllResp = new BaseDownloadAllResp();
        if(StringUtils.isEmpty(tableName) || tableId==null || pageSize==null ){
            return baseDownloadAllResp;
        }
        String organSql =getOrganSql(organSign,tableName);
        String sql = MessageFormat.format(listDataSql,tableName,tableId+"",organSql,pageSize+"");
        List<Map<String,Object>> queryResults = jdbcTemplate.queryForList(sql);
        List<Object> data = null;
        Long maxTableId = 0L;
        if(!CollectionUtils.isEmpty(queryResults)){
            data = new ArrayList<>(queryResults.size());
            for(Map<String,Object> row : queryResults){
                data.add(toJaveNameMap(row));
            }
            maxTableId =  Long.valueOf(  queryResults.get(queryResults.size()-1).get(DEFAULT_ID_COL_NAME).toString());

        }else{
            data = new ArrayList<>();
        }
        baseDownloadAllResp.setMaxTableId(maxTableId);
        baseDownloadAllResp.setData(data);
        return baseDownloadAllResp;
    }
    /**
     * 查询从updateTime开始的n条数据
     * @param tableName
     * @param updateTime
     * @param organSign
     * @param pageSize
     * @return
     */
    public BaseDownloadAllResp listDataByUpdateTime(String tableName,Date updateTime ,String organSign,Integer pageSize){
        BaseDownloadAllResp baseDownloadAllResp = new BaseDownloadAllResp();
        if(StringUtils.isEmpty(tableName) || updateTime == null || pageSize == null || noUpdateTimeTables.contains(tableName)){
            return baseDownloadAllResp;
        }
        String organSql =getOrganSql(organSign,tableName);

        String sql = MessageFormat.format(listDataByUpdateTimeSql,tableName,organSql,pageSize+"");
        List<Map<String,Object>> queryResults = jdbcTemplate.queryForList(sql,updateTime);
        List<Object> data = null;
        Date maxUpdateTime = null;
        if(!CollectionUtils.isEmpty(queryResults)){
            data = new ArrayList<>(queryResults.size());
            for(Map<String,Object> row : queryResults){
                data.add(toJaveNameMap(row));
            }
            maxUpdateTime = (Date) queryResults.get(queryResults.size()-1).get(DEFAULT_UPDATE_TIME_COL_NAME);

        }else{
            data = new ArrayList<>();
        }
        baseDownloadAllResp.setMaxUpdateTime(maxUpdateTime);
        baseDownloadAllResp.setData(data);
        return baseDownloadAllResp;
    }
    /**
     * 查询 等于 updateTime的n条数据
     * @param tableName
     * @param updateTime
     * @param organSign
     * @return
     */
    public BaseDownloadAllResp listDataEqualUpdateTime(String tableName,Date updateTime ,String organSign){
        BaseDownloadAllResp baseDownloadAllResp = new BaseDownloadAllResp();
        if(StringUtils.isEmpty(tableName) || updateTime == null || noUpdateTimeTables.contains(tableName) ){
            return baseDownloadAllResp;
        }
        String organSql =getOrganSql(organSign,tableName);
        String sql = MessageFormat.format(listDataEqualUpdateTimeSql,tableName,organSql);
        List<Map<String,Object>> queryResults = jdbcTemplate.queryForList(sql,updateTime);
        List<Object> data = null;
        Date maxUpdateTime = null;
        if(!CollectionUtils.isEmpty(queryResults)){
            data = new ArrayList<>(queryResults.size());
            for(Map<String,Object> row : queryResults){
                data.add(toJaveNameMap(row));
            }
            maxUpdateTime = (Date) queryResults.get(queryResults.size()-1).get(DEFAULT_UPDATE_TIME_COL_NAME);

        }else{
            data = new ArrayList<>();
        }
        baseDownloadAllResp.setMaxUpdateTime(maxUpdateTime);
        baseDownloadAllResp.setData(data);
        return baseDownloadAllResp;
    }

    /**
     * 将数据库字段名转换成javabean字段名
     * @param columName
     * @return
     */
    public String toJavaFiledName(String columName){
        String[] words = columName.split("_");
        StringBuffer name = new StringBuffer(words[0]);
        for(int i=1;i<words.length;i++){
            name.append(words[i].substring(0,1).toUpperCase()).append(words[i].substring(1));
        }
        return name.toString();
    }

    /**
     * 将数据库字段名为key的map转成java字段名为key的map
     * @return
     */
    public Map<String,Object> toJaveNameMap(Map<String,Object> columMap){
        Map<String,Object> retMap = new HashMap<>(columMap.size());
        Iterator<Map.Entry<String,Object>> iterator = columMap.entrySet().iterator();
        while(iterator.hasNext()){
            Map.Entry<String,Object> element = iterator.next();
            retMap.put(toJavaFiledName(element.getKey()),element.getValue());
        }
        return  retMap;
    }
    private String getOrganSql(String organSign,String tableName){
        String organSql = "";
        if(!StringUtils.isEmpty(organSign)){
            String orgColName = DEFAULT_ORGAN_COL_NAME;
            if(organColumNameMap.containsKey(tableName)){
                orgColName = organColumNameMap.get(tableName);
            }
            organSql = " and " + orgColName +" = '"+organSign+"'";
        }
        return  organSql;
    }


    public static void main(String[] args) {
        List<Long> list = new ArrayList<>();
        list.add(1l);list.add(2L);
        list.add(3l);
        System.out.println( list.stream().map(String::valueOf).collect(Collectors.joining(",")));
        BaseDataSyncService BaseDataSyncService =  new BaseDataSyncService();
        System.out.println(BaseDataSyncService.toJavaFiledName("saas_name_fir_end"));
    }

    private void init(){

        organColumNameMap.put("saas_product_baseinfo","organSign");
        organColumNameMap.put("saas_member_level","organSign");
        organColumNameMap.put("saas_member_base","organSign");
        organColumNameMap.put("saas_member_point_exchange","organSign");
        organColumNameMap.put("saas_member_prepay_card","organSign");
        organColumNameMap.put("saas_member_level_pointexchange_relation","organSign");
        organColumNameMap.put("saas_member_prepay_card_config","organSign");
        //order 库
        organColumNameMap.put("saas_prescription","organSign");
        organColumNameMap.put("saas_prescription_detail","organSign");

        //------------------
        noUpdateTimeTables.add("saas_member_prepay_card_config");
        noUpdateTimeTables.add("saas_member_exchange_product");
        noUpdateTimeTables.add("saas_member_level_pointexchange_relation");
    }
}
