package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Schema(description = "管理后台 - 门店用户关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantUserRelationPageReqVO extends PageParam {

    @Schema(description = "userId", example = "1")
    private Long userId;

    @Schema(description = "门店名", example = "芋艿")
    @Length(max = 64, message = "门店名最大长度为 {value}")
    private String name;

    @Schema(description = "联系手机")
    @Length(max = 32, message = "联系手机最大长度为 {value}")
    private String contactMobile;

    @Schema(description = "角色Code")
    private String roleCode;
    /**
     * 角色Ids
     */
    private List<Long> roleIds;

    /**
     * 可见的租户ids
     */
    private List<Long> tenantIds;

}
